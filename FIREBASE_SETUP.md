# إعداد Firebase للتطبيق

## 🔥 **خطوات إعداد Firebase:**

### 1. **إنشاء مشروع Firebase:**
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إضافة مشروع" (Add project)
3. أدخل اسم المشروع: `easy-shop-store`
4. اختر إعدادات Google Analytics (اختياري)
5. انقر "إنشاء مشروع"

### 2. **إعداد Firestore Database:**
1. من القائمة الجانبية، انقر على "Firestore Database"
2. انقر "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية
4. اختر المنطقة الجغرافية (مثل: europe-west3)

### 3. **إعداد Firebase Storage:**
1. من القائمة الجانبية، انقر على "Storage"
2. انقر "البدء"
3. اختر "Start in test mode"
4. اختر نفس المنطقة الجغرافية

### 4. **إعداد Firebase Authentication:**
1. من القائمة الجانبية، انقر على "Authentication"
2. انقر على تبويب "Sign-in method"
3. فعّل "Email/Password"
4. احفظ الإعدادات

### 5. **إضافة تطبيق ويب:**
1. في صفحة المشروع الرئيسية، انقر على أيقونة الويب `</>`
2. أدخل اسم التطبيق: `Easy Shop Web`
3. فعّل "Firebase Hosting" (اختياري)
4. انقر "تسجيل التطبيق"

### 6. **نسخ إعدادات Firebase:**
بعد إنشاء التطبيق، ستحصل على كود مثل هذا:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyC...",
  authDomain: "easy-shop-store.firebaseapp.com",
  projectId: "easy-shop-store",
  storageBucket: "easy-shop-store.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};
```

### 7. **تحديث ملف Firebase في التطبيق:**
1. افتح ملف `src/lib/firebase.ts`
2. استبدل `firebaseConfig` بالإعدادات الخاصة بك
3. احفظ الملف

### 8. **إعداد قواعد Firestore:**
في Firebase Console، اذهب إلى Firestore Database > Rules وضع هذه القواعد:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المنتجات
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // قواعد الإعلانات
    match /ads/{adId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد الإعدادات
    match /settings/{settingId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // قواعد الفئات
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### 9. **إعداد قواعد Storage:**
في Firebase Console، اذهب إلى Storage > Rules وضع هذه القواعد:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

## 🚀 **بعد الإعداد:**

### **تسجيل الدخول الأول:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

### **إنشاء حساب المدير الأول:**
1. سجل دخول بالحساب الافتراضي
2. اذهب إلى "إدارة المستخدمين"
3. أنشئ حساب مدير جديد
4. سجل خروج ودخول بالحساب الجديد

## 📊 **هيكل قاعدة البيانات:**

### **Collections:**
- `products` - المنتجات
- `ads` - الإعلانات
- `users` - المستخدمين
- `categories` - فئات المنتجات
- `settings` - إعدادات المتجر

### **Storage:**
- `products/` - صور المنتجات
- `ads/` - صور الإعلانات
- `logos/` - شعارات المتجر

## ⚠️ **ملاحظات مهمة:**

1. **الأمان:** غيّر قواعد Firestore لتكون أكثر أماناً في الإنتاج
2. **النسخ الاحتياطي:** فعّل النسخ الاحتياطي التلقائي
3. **المراقبة:** راقب استخدام Firebase لتجنب تجاوز الحدود المجانية
4. **الفهرسة:** أنشئ فهارس مركبة حسب الحاجة

## 🔧 **استكمال الإعداد:**

بعد ربط Firebase، ستتمكن من:
- ✅ حفظ المنتجات والإعلانات
- ✅ إدارة المستخدمين
- ✅ حفظ إعدادات المتجر
- ✅ رفع الصور
- ✅ مزامنة البيانات في الوقت الفعلي
