
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { CartProvider } from "./contexts/CartContext";
import { AuthProvider } from "./contexts/AuthContext";
import { AdsProvider } from "./contexts/AdsContext";
import { ProductsProvider } from "./contexts/ProductsContext";
import { ChatProvider } from "./contexts/ChatContext";
import { NotificationsProvider } from "./contexts/NotificationsContext";
import { ThemeProvider } from "./components/ThemeProvider";
import ChatButton from "./components/ChatButton";
import Index from "./pages/Index";
import Products from "./pages/Products";
import ProductDetails from "./pages/ProductDetails";
import Cart from "./pages/Cart";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";
import AdminDashboard from "./pages/AdminDashboard";
import MerchantDashboard from "./pages/MerchantDashboard";
import Login from "./pages/auth/Login";
import Register from "./pages/auth/Register";
import AccountSettingsPage from "./pages/AccountSettings";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <ThemeProvider>
        <AuthProvider>
          <NotificationsProvider>
            <AdsProvider>
              <ProductsProvider>
                <ChatProvider>
                  <CartProvider>
                  <div className="min-h-screen w-full">
                    <Toaster />
                    <Sonner />
                    <BrowserRouter>
                      <Routes>
                        <Route path="/" element={<Index />} />
                        <Route path="/products" element={<Products />} />
                        <Route path="/products/:id" element={<ProductDetails />} />
                        <Route path="/cart" element={<Cart />} />
                        <Route path="/contact" element={<Contact />} />
                        <Route path="/login" element={<Login />} />
                        <Route path="/register" element={<Register />} />
                        <Route path="/account" element={<AccountSettingsPage />} />
                        <Route path="/admin" element={<AdminDashboard />} />
                        <Route path="/merchant" element={<MerchantDashboard />} />
                        <Route path="*" element={<NotFound />} />
                      </Routes>
                      <ChatButton />
                    </BrowserRouter>
                  </div>
                </CartProvider>
              </ChatProvider>
            </ProductsProvider>
          </AdsProvider>
        </NotificationsProvider>
      </AuthProvider>
      </ThemeProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
