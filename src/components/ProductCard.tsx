
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { useCart, Product } from '@/contexts/CartContext';
import { useToast } from '@/hooks/use-toast';
import { ShoppingCart, Eye } from 'lucide-react';

interface ProductCardProps {
  product: Product;
  showDetails?: boolean;
}

const ProductCard = ({ product, showDetails = true }: ProductCardProps) => {
  const { dispatch } = useCart();
  const { toast } = useToast();

  const handleAddToCart = () => {
    dispatch({ type: 'ADD_TO_CART', product });
    toast({
      title: "تم إضافة المنتج",
      description: `تم إضافة ${product.name} إلى السلة بنجاح`,
    });
  };

  return (
    <Card className="h-full hover:shadow-2xl transition-all duration-300 hover:scale-105 bg-white dark:bg-gray-900 border-0 shadow-lg rounded-2xl overflow-hidden group">
      <CardContent className="p-3">
        <div className="aspect-square mb-3 overflow-hidden rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 relative">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
          />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300"></div>
        </div>
        <h3 className="font-bold text-sm mb-2 text-right text-gray-900 dark:text-white line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          {product.name}
        </h3>
        <p className="text-lg font-bold text-right mb-2">
          <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
            {product.price} ج.م
          </span>
        </p>
      </CardContent>
      <CardFooter className="p-3 pt-0 flex flex-col gap-2">
        <Button 
          onClick={handleAddToCart}
          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-xs py-2"
        >
          <ShoppingCart className="mr-1 h-3 w-3" />
          أضف للسلة
        </Button>
        {showDetails && (
          <Link to={`/products/${product.id}`} className="w-full">
            <Button variant="outline" className="w-full rounded-xl border-2 hover:scale-105 transition-all duration-300 text-xs py-2">
              <Eye className="mr-1 h-3 w-3" />
              التفاصيل
            </Button>
          </Link>
        )}
      </CardFooter>
    </Card>
  );
};

export default ProductCard;
