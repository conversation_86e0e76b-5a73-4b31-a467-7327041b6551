import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { useAds } from '@/contexts/AdsContext';
import { useToast } from '@/hooks/use-toast';
import { Plus, Edit, Trash2, Eye, EyeOff, Megaphone } from 'lucide-react';

const AdminAds = () => {
  const { state, addAd, updateAd, deleteAd, toggleAdStatus } = useAds();
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAd, setEditingAd] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image: '',
    link: '',
    isActive: true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.description || !formData.image) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    if (editingAd) {
      updateAd({
        ...editingAd,
        ...formData,
      });
      toast({
        title: "تم تحديث الإعلان",
        description: "تم تحديث الإعلان بنجاح",
      });
    } else {
      addAd(formData);
      toast({
        title: "تم إضافة الإعلان",
        description: "تم إضافة الإعلان بنجاح",
      });
    }

    setFormData({
      title: '',
      description: '',
      image: '',
      link: '',
      isActive: true,
    });
    setEditingAd(null);
    setIsDialogOpen(false);
  };

  const handleEdit = (ad) => {
    setEditingAd(ad);
    setFormData({
      title: ad.title,
      description: ad.description,
      image: ad.image,
      link: ad.link || '',
      isActive: ad.isActive,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (adId: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الإعلان؟')) {
      deleteAd(adId);
      toast({
        title: "تم حذف الإعلان",
        description: "تم حذف الإعلان بنجاح",
      });
    }
  };

  const handleToggleStatus = (adId: number) => {
    toggleAdStatus(adId);
    toast({
      title: "تم تحديث حالة الإعلان",
      description: "تم تحديث حالة الإعلان بنجاح",
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            إدارة الإعلانات
          </h2>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            ({state.ads.length} إعلان)
          </span>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button 
              onClick={() => {
                setEditingAd(null);
                setFormData({
                  title: '',
                  description: '',
                  image: '',
                  link: '',
                  isActive: true,
                });
              }}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Plus className="mr-2 h-4 w-4" />
              إضافة إعلان جديد
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="text-right">
                {editingAd ? 'تعديل الإعلان' : 'إضافة إعلان جديد'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                  عنوان الإعلان *
                </label>
                <Input
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="أدخل عنوان الإعلان"
                  className="text-right"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                  وصف الإعلان *
                </label>
                <Textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="أدخل وصف الإعلان"
                  className="text-right"
                  rows={3}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                  رابط الصورة *
                </label>
                <Input
                  name="image"
                  value={formData.image}
                  onChange={handleChange}
                  placeholder="https://example.com/image.jpg"
                  className="text-right"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                  رابط الإعلان (اختياري)
                </label>
                <Input
                  name="link"
                  value={formData.link}
                  onChange={handleChange}
                  placeholder="/products"
                  className="text-right"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Switch
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                />
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  إعلان نشط
                </label>
              </div>
              
              <div className="flex gap-2">
                <Button type="submit" className="flex-1">
                  {editingAd ? 'تحديث' : 'إضافة'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsDialogOpen(false)}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Ads List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {state.ads.map((ad) => (
          <Card key={ad.id} className="overflow-hidden">
            <div className="aspect-video bg-gray-200 dark:bg-gray-700">
              <img
                src={ad.image}
                alt={ad.title}
                className="w-full h-full object-cover"
              />
            </div>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{ad.title}</CardTitle>
                <div className="flex items-center gap-2">
                  {ad.isActive ? (
                    <Eye className="h-4 w-4 text-green-600" />
                  ) : (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  )}
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    ad.isActive 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {ad.isActive ? 'نشط' : 'غير نشط'}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 text-right">
                {ad.description}
              </p>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleEdit(ad)}
                  className="flex-1"
                >
                  <Edit className="mr-1 h-3 w-3" />
                  تعديل
                </Button>
                <Button
                  size="sm"
                  variant={ad.isActive ? "secondary" : "default"}
                  onClick={() => handleToggleStatus(ad.id)}
                  className="flex-1"
                >
                  {ad.isActive ? 'إخفاء' : 'إظهار'}
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleDelete(ad.id)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {state.ads.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Megaphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              لا توجد إعلانات
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              ابدأ بإضافة إعلان جديد لعرضه في الصفحة الرئيسية
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdminAds;
