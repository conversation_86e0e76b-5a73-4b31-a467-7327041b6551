import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { 
  Settings, 
  Store, 
  Palette, 
  Upload, 
  X, 
  Save,
  Truck,
  CreditCard,
  Shield,
  Star,
  Phone,
  Mail,
  MapPin
} from 'lucide-react';

const AdminSettings = () => {
  const { toast } = useToast();
  const logoInputRef = useRef<HTMLInputElement>(null);
  const [logoPreview, setLogoPreview] = useState<string>('/logo.png');

  // إعدادات المتجر العامة
  const [storeSettings, setStoreSettings] = useState({
    storeName: 'Easy Shop',
    storeDescription: 'متجرك الإلكتروني المفضل للتسوق الآمن والسريع',
    storeEmail: '<EMAIL>',
    storePhone: '+20 ************',
    storeAddress: 'القاهرة، مصر',
    currency: 'ج.م',
    language: 'ar',
    timezone: 'Africa/Cairo'
  });

  // إعدادات المميزات السفلية
  const [features, setFeatures] = useState([
    {
      id: 1,
      icon: 'Truck',
      title: 'شحن سريع',
      description: 'توصيل مجاني للطلبات أكثر من 500 ج.م',
      isActive: true
    },
    {
      id: 2,
      icon: 'CreditCard',
      title: 'دفع آمن',
      description: 'طرق دفع متعددة وآمنة 100%',
      isActive: true
    },
    {
      id: 3,
      icon: 'Shield',
      title: 'ضمان الجودة',
      description: 'ضمان استرداد المال خلال 30 يوم',
      isActive: true
    },
    {
      id: 4,
      icon: 'Star',
      title: 'خدمة عملاء ممتازة',
      description: 'دعم فني على مدار 24/7',
      isActive: true
    }
  ]);

  // إعدادات الألوان والتصميم
  const [designSettings, setDesignSettings] = useState({
    primaryColor: '#3B82F6',
    secondaryColor: '#8B5CF6',
    accentColor: '#10B981',
    backgroundColor: '#F9FAFB',
    textColor: '#1F2937',
    headerStyle: 'gradient',
    footerStyle: 'dark',
    borderRadius: 'medium'
  });

  // إعدادات الدفع والشحن
  const [paymentSettings, setPaymentSettings] = useState({
    enableCashOnDelivery: true,
    enableCreditCard: true,
    enablePayPal: false,
    enableBankTransfer: true,
    freeShippingThreshold: 500,
    shippingCost: 50,
    taxRate: 14
  });

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLogoPreview(result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveLogo = () => {
    setLogoPreview('/logo.png');
    if (logoInputRef.current) {
      logoInputRef.current.value = '';
    }
  };

  const handleSaveSettings = () => {
    // هنا يتم حفظ الإعدادات في قاعدة البيانات
    toast({
      title: "تم حفظ الإعدادات",
      description: "تم حفظ جميع إعدادات المتجر بنجاح",
    });
  };

  const updateFeature = (id: number, field: string, value: any) => {
    setFeatures(features.map(feature => 
      feature.id === id ? { ...feature, [field]: value } : feature
    ));
  };

  const getIconComponent = (iconName: string) => {
    const icons = {
      Truck: Truck,
      CreditCard: CreditCard,
      Shield: Shield,
      Star: Star
    };
    const IconComponent = icons[iconName] || Star;
    return <IconComponent className="h-6 w-6" />;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          إعدادات المتجر
        </h2>
        <Button onClick={handleSaveSettings} className="bg-gradient-to-r from-blue-600 to-purple-600">
          <Save className="mr-2 h-4 w-4" />
          حفظ جميع الإعدادات
        </Button>
      </div>

      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">عام</TabsTrigger>
          <TabsTrigger value="design">التصميم</TabsTrigger>
          <TabsTrigger value="features">المميزات</TabsTrigger>
          <TabsTrigger value="payment">الدفع والشحن</TabsTrigger>
          <TabsTrigger value="contact">التواصل</TabsTrigger>
        </TabsList>

        {/* الإعدادات العامة */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Store className="h-5 w-5" />
                معلومات المتجر الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-right">اسم المتجر</Label>
                  <Input
                    value={storeSettings.storeName}
                    onChange={(e) => setStoreSettings({...storeSettings, storeName: e.target.value})}
                    className="text-right"
                  />
                </div>
                <div>
                  <Label className="text-right">العملة</Label>
                  <Input
                    value={storeSettings.currency}
                    onChange={(e) => setStoreSettings({...storeSettings, currency: e.target.value})}
                    className="text-right"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-right">وصف المتجر</Label>
                <Textarea
                  value={storeSettings.storeDescription}
                  onChange={(e) => setStoreSettings({...storeSettings, storeDescription: e.target.value})}
                  className="text-right"
                  rows={3}
                />
              </div>

              {/* شعار المتجر */}
              <div>
                <Label className="text-right">شعار المتجر</Label>
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => logoInputRef.current?.click()}
                      className="flex-1"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      اختر شعار جديد
                    </Button>
                    {logoPreview !== '/logo.png' && (
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={handleRemoveLogo}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  
                  <input
                    ref={logoInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleLogoChange}
                    className="hidden"
                  />
                  
                  {logoPreview && (
                    <div className="flex justify-center">
                      <img
                        src={logoPreview}
                        alt="شعار المتجر"
                        className="h-20 w-auto object-contain border rounded-lg p-2"
                      />
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* إعدادات التصميم */}
        <TabsContent value="design" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                ألوان المتجر
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-right">اللون الأساسي</Label>
                  <div className="flex gap-2">
                    <input
                      type="color"
                      value={designSettings.primaryColor}
                      onChange={(e) => setDesignSettings({...designSettings, primaryColor: e.target.value})}
                      className="w-12 h-10 rounded border"
                    />
                    <Input
                      value={designSettings.primaryColor}
                      onChange={(e) => setDesignSettings({...designSettings, primaryColor: e.target.value})}
                      className="text-right"
                    />
                  </div>
                </div>
                <div>
                  <Label className="text-right">اللون الثانوي</Label>
                  <div className="flex gap-2">
                    <input
                      type="color"
                      value={designSettings.secondaryColor}
                      onChange={(e) => setDesignSettings({...designSettings, secondaryColor: e.target.value})}
                      className="w-12 h-10 rounded border"
                    />
                    <Input
                      value={designSettings.secondaryColor}
                      onChange={(e) => setDesignSettings({...designSettings, secondaryColor: e.target.value})}
                      className="text-right"
                    />
                  </div>
                </div>
                <div>
                  <Label className="text-right">لون التمييز</Label>
                  <div className="flex gap-2">
                    <input
                      type="color"
                      value={designSettings.accentColor}
                      onChange={(e) => setDesignSettings({...designSettings, accentColor: e.target.value})}
                      className="w-12 h-10 rounded border"
                    />
                    <Input
                      value={designSettings.accentColor}
                      onChange={(e) => setDesignSettings({...designSettings, accentColor: e.target.value})}
                      className="text-right"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* إعدادات المميزات */}
        <TabsContent value="features" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>مميزات المتجر (تظهر في أسفل الصفحة)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {features.map((feature) => (
                <Card key={feature.id} className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <Switch
                      checked={feature.isActive}
                      onCheckedChange={(checked) => updateFeature(feature.id, 'isActive', checked)}
                    />
                    <div className="flex items-center gap-2">
                      {getIconComponent(feature.icon)}
                      <span className="font-semibold">{feature.title}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <Label className="text-right">العنوان</Label>
                      <Input
                        value={feature.title}
                        onChange={(e) => updateFeature(feature.id, 'title', e.target.value)}
                        className="text-right"
                      />
                    </div>
                    <div>
                      <Label className="text-right">الوصف</Label>
                      <Input
                        value={feature.description}
                        onChange={(e) => updateFeature(feature.id, 'description', e.target.value)}
                        className="text-right"
                      />
                    </div>
                  </div>
                </Card>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* إعدادات الدفع والشحن */}
        <TabsContent value="payment" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>طرق الدفع والشحن</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">طرق الدفع</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Switch
                        checked={paymentSettings.enableCashOnDelivery}
                        onCheckedChange={(checked) => setPaymentSettings({...paymentSettings, enableCashOnDelivery: checked})}
                      />
                      <Label>الدفع عند الاستلام</Label>
                    </div>
                    <div className="flex items-center justify-between">
                      <Switch
                        checked={paymentSettings.enableCreditCard}
                        onCheckedChange={(checked) => setPaymentSettings({...paymentSettings, enableCreditCard: checked})}
                      />
                      <Label>بطاقة ائتمان</Label>
                    </div>
                    <div className="flex items-center justify-between">
                      <Switch
                        checked={paymentSettings.enableBankTransfer}
                        onCheckedChange={(checked) => setPaymentSettings({...paymentSettings, enableBankTransfer: checked})}
                      />
                      <Label>تحويل بنكي</Label>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-semibold">إعدادات الشحن</h4>
                  <div>
                    <Label className="text-right">الحد الأدنى للشحن المجاني (ج.م)</Label>
                    <Input
                      type="number"
                      value={paymentSettings.freeShippingThreshold}
                      onChange={(e) => setPaymentSettings({...paymentSettings, freeShippingThreshold: parseInt(e.target.value)})}
                      className="text-right"
                    />
                  </div>
                  <div>
                    <Label className="text-right">تكلفة الشحن (ج.م)</Label>
                    <Input
                      type="number"
                      value={paymentSettings.shippingCost}
                      onChange={(e) => setPaymentSettings({...paymentSettings, shippingCost: parseInt(e.target.value)})}
                      className="text-right"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* معلومات التواصل */}
        <TabsContent value="contact" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                معلومات التواصل
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-right flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    البريد الإلكتروني
                  </Label>
                  <Input
                    value={storeSettings.storeEmail}
                    onChange={(e) => setStoreSettings({...storeSettings, storeEmail: e.target.value})}
                    className="text-right"
                  />
                </div>
                <div>
                  <Label className="text-right flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    رقم الهاتف
                  </Label>
                  <Input
                    value={storeSettings.storePhone}
                    onChange={(e) => setStoreSettings({...storeSettings, storePhone: e.target.value})}
                    className="text-right"
                  />
                </div>
              </div>
              <div>
                <Label className="text-right flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  العنوان
                </Label>
                <Textarea
                  value={storeSettings.storeAddress}
                  onChange={(e) => setStoreSettings({...storeSettings, storeAddress: e.target.value})}
                  className="text-right"
                  rows={2}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminSettings;
