import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import { MessageCircle, Send, X, Users, ShoppingBag, User } from 'lucide-react';

interface ChatMessage {
  id: number;
  senderId: number;
  senderName: string;
  senderRole: 'admin' | 'merchant' | 'customer';
  message: string;
  timestamp: Date;
  isRead: boolean;
}

interface ChatUser {
  id: number;
  name: string;
  email: string;
  role: 'merchant' | 'customer';
  isOnline: boolean;
  lastMessage?: string;
  unreadCount: number;
}

const AdminChat = () => {
  const { state } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<ChatUser | null>(null);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock users data
  const [chatUsers] = useState<ChatUser[]>([
    {
      id: 2,
      name: 'تاجر الإلكترونيات',
      email: '<EMAIL>',
      role: 'merchant',
      isOnline: true,
      lastMessage: 'مرحباً، أريد إضافة منتجات جديدة',
      unreadCount: 2
    },
    {
      id: 3,
      name: 'زبون عادي',
      email: '<EMAIL>',
      role: 'customer',
      isOnline: false,
      lastMessage: 'هل يمكنني إرجاع المنتج؟',
      unreadCount: 1
    },
    {
      id: 4,
      name: 'تاجر الملابس',
      email: '<EMAIL>',
      role: 'merchant',
      isOnline: true,
      lastMessage: 'شكراً لك',
      unreadCount: 0
    },
    {
      id: 5,
      name: 'زبون VIP',
      email: '<EMAIL>',
      role: 'customer',
      isOnline: true,
      lastMessage: 'أريد خصم خاص',
      unreadCount: 3
    }
  ]);

  // Mock messages data
  const [allMessages] = useState<ChatMessage[]>([
    {
      id: 1,
      senderId: 2,
      senderName: 'تاجر الإلكترونيات',
      senderRole: 'merchant',
      message: 'مرحباً، أريد إضافة منتجات جديدة',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      isRead: false
    },
    {
      id: 2,
      senderId: 1,
      senderName: 'المدير العام',
      senderRole: 'admin',
      message: 'مرحباً، يمكنك إضافة المنتجات من لوحة التحكم',
      timestamp: new Date(Date.now() - 25 * 60 * 1000),
      isRead: true
    },
    {
      id: 3,
      senderId: 2,
      senderName: 'تاجر الإلكترونيات',
      senderRole: 'merchant',
      message: 'شكراً، هل يمكنني الحصول على مساعدة في رفع الصور؟',
      timestamp: new Date(Date.now() - 20 * 60 * 1000),
      isRead: false
    },
    {
      id: 4,
      senderId: 3,
      senderName: 'زبون عادي',
      senderRole: 'customer',
      message: 'هل يمكنني إرجاع المنتج؟',
      timestamp: new Date(Date.now() - 60 * 60 * 1000),
      isRead: false
    }
  ]);

  const totalUnreadCount = chatUsers.reduce((total, user) => total + user.unreadCount, 0);

  useEffect(() => {
    if (selectedUser) {
      const userMessages = allMessages.filter(
        msg => msg.senderId === selectedUser.id || 
               (msg.senderId === state.user?.id && msg.senderName === state.user?.name)
      );
      setMessages(userMessages);
    }
  }, [selectedUser, allMessages, state.user]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (!message.trim() || !selectedUser) return;

    const newMessage: ChatMessage = {
      id: Date.now(),
      senderId: state.user?.id || 1,
      senderName: state.user?.name || 'المدير',
      senderRole: 'admin',
      message: message.trim(),
      timestamp: new Date(),
      isRead: false
    };

    setMessages([...messages, newMessage]);
    setMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'merchant':
        return <ShoppingBag className="h-3 w-3 text-blue-600" />;
      default:
        return <User className="h-3 w-3 text-green-600" />;
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'merchant':
        return 'تاجر';
      default:
        return 'زبون';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-EG', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  // Only show chat for admin users
  if (state.user?.role !== 'admin') {
    return null;
  }

  return (
    <>
      {/* Chat Button */}
      <div className="fixed bottom-6 left-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="h-14 w-14 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg"
          size="icon"
        >
          <MessageCircle className="h-6 w-6" />
          {totalUnreadCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
              {totalUnreadCount > 9 ? '9+' : totalUnreadCount}
            </span>
          )}
        </Button>
      </div>

      {/* Chat Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl h-[600px] p-0">
          <div className="flex h-full">
            {/* Users List */}
            <div className="w-1/3 border-r border-gray-200 dark:border-gray-700">
              <DialogHeader className="p-4 border-b border-gray-200 dark:border-gray-700">
                <DialogTitle className="text-right flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  المحادثات ({chatUsers.length})
                </DialogTitle>
              </DialogHeader>
              <ScrollArea className="h-[calc(600px-80px)]">
                <div className="p-2">
                  {chatUsers.map((user) => (
                    <Card
                      key={user.id}
                      className={`mb-2 cursor-pointer transition-colors ${
                        selectedUser?.id === user.id 
                          ? 'bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700' 
                          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}
                      onClick={() => setSelectedUser(user)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="text-xs">
                                {user.name.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex items-center gap-1">
                              {getRoleIcon(user.role)}
                              <span className={`w-2 h-2 rounded-full ${
                                user.isOnline ? 'bg-green-500' : 'bg-gray-400'
                              }`} />
                            </div>
                          </div>
                          {user.unreadCount > 0 && (
                            <span className="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                              {user.unreadCount}
                            </span>
                          )}
                        </div>
                        <div className="text-right">
                          <h4 className="font-semibold text-sm">{user.name}</h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {getRoleLabel(user.role)}
                          </p>
                          {user.lastMessage && (
                            <p className="text-xs text-gray-600 dark:text-gray-300 mt-1 truncate">
                              {user.lastMessage}
                            </p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </div>

            {/* Chat Area */}
            <div className="flex-1 flex flex-col">
              {selectedUser ? (
                <>
                  {/* Chat Header */}
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedUser(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                      <div className="text-right">
                        <h3 className="font-semibold">{selectedUser.name}</h3>
                        <div className="flex items-center gap-2 justify-end">
                          {getRoleIcon(selectedUser.role)}
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {getRoleLabel(selectedUser.role)}
                          </span>
                          <span className={`w-2 h-2 rounded-full ${
                            selectedUser.isOnline ? 'bg-green-500' : 'bg-gray-400'
                          }`} />
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {selectedUser.isOnline ? 'متصل' : 'غير متصل'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  <ScrollArea className="flex-1 p-4">
                    <div className="space-y-4">
                      {messages.map((msg) => (
                        <div
                          key={msg.id}
                          className={`flex ${
                            msg.senderId === state.user?.id ? 'justify-start' : 'justify-end'
                          }`}
                        >
                          <div
                            className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                              msg.senderId === state.user?.id
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'
                            }`}
                          >
                            <p className="text-sm">{msg.message}</p>
                            <p className={`text-xs mt-1 ${
                              msg.senderId === state.user?.id
                                ? 'text-blue-100'
                                : 'text-gray-500 dark:text-gray-400'
                            }`}>
                              {formatTime(msg.timestamp)}
                            </p>
                          </div>
                        </div>
                      ))}
                      <div ref={messagesEndRef} />
                    </div>
                  </ScrollArea>

                  {/* Message Input */}
                  <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex gap-2">
                      <Button
                        onClick={handleSendMessage}
                        disabled={!message.trim()}
                        size="icon"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                      <Input
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="اكتب رسالتك..."
                        className="text-right"
                      />
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      اختر محادثة
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      اختر مستخدماً من القائمة لبدء المحادثة
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AdminChat;
