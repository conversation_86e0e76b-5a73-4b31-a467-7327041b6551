
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { ShoppingCart, Store, Moon, Sun, User, Settings, LogOut, UserPlus, LogIn, Shield, Package } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/components/ThemeProvider';
import { useToast } from '@/hooks/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const Header = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { state } = useCart();
  const { state: authState, logout } = useAuth();
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  
  const navItems = [
    { name: 'الرئيسية', path: '/' },
    { name: 'المنتجات', path: '/products' },
    { name: 'تواصل معنا', path: '/contact' },
  ];

  return (
    <header className="bg-white dark:bg-gray-900 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 backdrop-blur-md bg-opacity-90 dark:bg-opacity-90">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 space-x-reverse">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-xl">
              <Store className="h-6 w-6 text-white" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Easy Shop
            </span>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-6 space-x-reverse">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105 ${
                  location.pathname === item.path
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Right Section */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Theme Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
              className="rounded-xl hover:scale-105 transition-transform"
            >
              {theme === 'light' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
            </Button>

            {/* Cart */}
            <Link to="/cart">
              <Button 
                variant="outline" 
                size="sm" 
                className="relative rounded-xl hover:scale-105 transition-transform bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 border-blue-200 dark:border-blue-700"
              >
                <ShoppingCart className="h-4 w-4" />
                {state.items.length > 0 && (
                  <span className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
                    {state.items.reduce((sum, item) => sum + item.quantity, 0)}
                  </span>
                )}
              </Button>
            </Link>

            {/* Account Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="rounded-xl hover:scale-105 transition-transform bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-900 dark:to-teal-900 border-green-200 dark:border-green-700"
                >
                  <User className="h-4 w-4" />
                  {authState.user && (
                    <span className="mr-2 hidden md:inline">{authState.user.name}</span>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56 rounded-xl shadow-xl border-0 bg-white dark:bg-gray-900" align="end">
                {!authState.isAuthenticated ? (
                  <>
                    <DropdownMenuItem
                      className="rounded-lg m-1 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 text-blue-600 dark:text-blue-400"
                      onClick={() => navigate('/login')}
                    >
                      <LogIn className="mr-2 h-4 w-4" />
                      <span>تسجيل الدخول</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="rounded-lg m-1 cursor-pointer hover:bg-green-50 dark:hover:bg-green-900 text-green-600 dark:text-green-400"
                      onClick={() => navigate('/register')}
                    >
                      <UserPlus className="mr-2 h-4 w-4" />
                      <span>إنشاء حساب جديد</span>
                    </DropdownMenuItem>
                  </>
                ) : (
                  <>
                    {authState.user?.role === 'admin' && (
                      <DropdownMenuItem
                        className="rounded-lg m-1 cursor-pointer hover:bg-purple-50 dark:hover:bg-purple-900 text-purple-600 dark:text-purple-400"
                        onClick={() => navigate('/admin')}
                      >
                        <Shield className="mr-2 h-4 w-4" />
                        <span>لوحة تحكم المدير</span>
                      </DropdownMenuItem>
                    )}
                    {authState.user?.role === 'merchant' && (
                      <DropdownMenuItem
                        className="rounded-lg m-1 cursor-pointer hover:bg-orange-50 dark:hover:bg-orange-900 text-orange-600 dark:text-orange-400"
                        onClick={() => navigate('/merchant')}
                      >
                        <Package className="mr-2 h-4 w-4" />
                        <span>لوحة تحكم التاجر</span>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator className="bg-gray-200 dark:bg-gray-700" />
                    <DropdownMenuItem
                      className="rounded-lg m-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                      onClick={() => navigate('/account')}
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      <span>إعدادات الحساب</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="bg-gray-200 dark:bg-gray-700" />
                    <DropdownMenuItem
                      className="rounded-lg m-1 cursor-pointer hover:bg-red-50 dark:hover:bg-red-900 text-red-600 dark:text-red-400"
                      onClick={() => {
                        logout();
                        toast({
                          title: "تم تسجيل الخروج",
                          description: "تم تسجيل خروجك بنجاح",
                        });
                        navigate('/');
                      }}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>تسجيل الخروج</span>
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Mobile Navigation */}
        <nav className="md:hidden pb-4">
          <div className="grid grid-cols-3 gap-2">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`text-center py-2 px-3 rounded-xl font-medium transition-all duration-300 ${
                  location.pathname === item.path
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header;
