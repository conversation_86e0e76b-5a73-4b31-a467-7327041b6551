import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import { MessageCircle, Minimize2 } from 'lucide-react';
import Chat from './Chat';

const ChatButton = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const { state: chatState } = useChat();
  const { state: authState } = useAuth();

  // Only show chat for authenticated users (admin and merchant)
  if (!authState.isAuthenticated || authState.user?.role === 'customer') {
    return null;
  }

  const handleToggleChat = () => {
    if (isMinimized) {
      setIsMinimized(false);
      setIsChatOpen(true);
    } else {
      setIsChatOpen(!isChatOpen);
    }
  };

  const handleMinimize = () => {
    setIsMinimized(true);
    setIsChatOpen(false);
  };

  const handleClose = () => {
    setIsChatOpen(false);
    setIsMinimized(false);
  };

  return (
    <>
      {/* Chat Button */}
      <div className="fixed bottom-4 left-4 z-40">
        <Button
          onClick={handleToggleChat}
          className={`relative rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-all duration-300 ${
            isChatOpen || isMinimized
              ? 'bg-gray-600 hover:bg-gray-700'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          {isMinimized ? (
            <Minimize2 className="h-6 w-6 text-white" />
          ) : (
            <MessageCircle className="h-6 w-6 text-white" />
          )}
          
          {/* Unread Badge */}
          {chatState.unreadTotal > 0 && !isChatOpen && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 flex items-center justify-center text-xs font-bold"
            >
              {chatState.unreadTotal > 99 ? '99+' : chatState.unreadTotal}
            </Badge>
          )}
        </Button>
      </div>

      {/* Chat Component */}
      <Chat 
        isOpen={isChatOpen} 
        onClose={handleClose}
        onMinimize={handleMinimize}
      />
    </>
  );
};

export default ChatButton;
