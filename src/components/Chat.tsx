import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import { Send, MessageCircle, X, Minimize2 } from 'lucide-react';

interface ChatProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize: () => void;
}

const Chat = ({ isOpen, onClose, onMinimize }: ChatProps) => {
  const { state: chatState, sendMessage, markAsRead, createConversation } = useChat();
  const { state: authState } = useAuth();
  const [newMessage, setNewMessage] = useState('');
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const currentUser = authState.user;
  if (!currentUser) return null;

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatState.messages, selectedConversation]);

  // Create conversation with admin if merchant doesn't have one
  useEffect(() => {
    if (currentUser.role === 'merchant') {
      const adminUser = { id: 1, name: 'المدير العام', role: 'admin' as const };
      const conversationId = createConversation(currentUser, adminUser);
      if (!selectedConversation) {
        setSelectedConversation(conversationId);
      }
    }
  }, [currentUser, createConversation, selectedConversation]);

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedConversation) return;

    const conversation = chatState.conversations.find(conv => conv.id === selectedConversation);
    if (!conversation) return;

    const receiver = conversation.participants.find(p => p.id !== currentUser.id);
    if (!receiver) return;

    sendMessage(selectedConversation, newMessage, currentUser.id, receiver.id);
    setNewMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('ar-EG', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const messageDate = new Date(date);
    
    if (messageDate.toDateString() === today.toDateString()) {
      return 'اليوم';
    }
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'أمس';
    }
    
    return new Intl.DateTimeFormat('ar-EG', {
      day: 'numeric',
      month: 'short',
    }).format(messageDate);
  };

  if (!isOpen) return null;

  const currentMessages = selectedConversation ? chatState.messages[selectedConversation] || [] : [];
  const currentConversation = chatState.conversations.find(conv => conv.id === selectedConversation);

  return (
    <div className="fixed bottom-4 left-4 w-96 h-[500px] bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 z-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-blue-600" />
          <h3 className="font-semibold text-gray-900 dark:text-white">
            {currentUser.role === 'admin' ? 'الرسائل' : 'التواصل مع الإدارة'}
          </h3>
          {chatState.unreadTotal > 0 && (
            <Badge variant="destructive" className="text-xs">
              {chatState.unreadTotal}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="sm" onClick={onMinimize}>
            <Minimize2 className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Conversations List (for admin) */}
      {currentUser.role === 'admin' && (
        <div className="border-b border-gray-200 dark:border-gray-700 max-h-32 overflow-y-auto">
          {chatState.conversations.map((conversation) => {
            const otherParticipant = conversation.participants.find(p => p.id !== currentUser.id);
            if (!otherParticipant) return null;

            return (
              <div
                key={conversation.id}
                className={`p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-100 dark:border-gray-800 ${
                  selectedConversation === conversation.id ? 'bg-blue-50 dark:bg-blue-900' : ''
                }`}
                onClick={() => {
                  setSelectedConversation(conversation.id);
                  markAsRead(conversation.id, currentUser.id);
                }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-sm text-gray-900 dark:text-white">
                      {otherParticipant.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {otherParticipant.role === 'merchant' ? 'تاجر' : 'زبون'}
                    </div>
                  </div>
                  {conversation.unreadCount > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {conversation.unreadCount}
                    </Badge>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 p-4 overflow-y-auto h-80">
        {currentMessages.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 mt-8">
            <MessageCircle className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>لا توجد رسائل بعد</p>
            <p className="text-sm">ابدأ محادثة جديدة</p>
          </div>
        ) : (
          <div className="space-y-4">
            {currentMessages.map((message, index) => {
              const isCurrentUser = message.senderId === currentUser.id;
              const showDate = index === 0 || 
                formatDate(message.timestamp) !== formatDate(currentMessages[index - 1].timestamp);

              return (
                <div key={message.id}>
                  {showDate && (
                    <div className="text-center text-xs text-gray-500 dark:text-gray-400 my-2">
                      {formatDate(message.timestamp)}
                    </div>
                  )}
                  <div className={`flex ${isCurrentUser ? 'justify-start' : 'justify-end'}`}>
                    <div
                      className={`max-w-xs px-3 py-2 rounded-lg ${
                        isCurrentUser
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'
                      }`}
                    >
                      <div className="text-sm">{message.message}</div>
                      <div className={`text-xs mt-1 ${
                        isCurrentUser ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                      }`}>
                        {formatTime(message.timestamp)}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Message Input */}
      {selectedConversation && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex gap-2">
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="اكتب رسالتك..."
              className="flex-1 text-right"
            />
            <Button onClick={handleSendMessage} size="sm" disabled={!newMessage.trim()}>
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Chat;
