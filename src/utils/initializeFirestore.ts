import { doc, setDoc, collection, addDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { db, auth } from '@/lib/firebase';

// إعدادات المتجر الافتراضية
const defaultStoreSettings = {
  storeName: 'Easy Shop',
  storeDescription: 'متجرك الإلكتروني المفضل للتسوق الآمن والسريع',
  storeEmail: '<EMAIL>',
  storePhone: '+20 ************',
  storeAddress: 'القاهرة، مصر',
  currency: 'ج.م',
  language: 'ar',
  timezone: 'Africa/Cairo',
  primaryColor: '#3B82F6',
  secondaryColor: '#8B5CF6',
  accentColor: '#10B981',
  features: [
    {
      id: 1,
      icon: 'Truck',
      title: 'شحن سريع',
      description: 'توصيل مجاني للطلبات أكثر من 500 ج.م',
      isActive: true
    },
    {
      id: 2,
      icon: 'CreditCard',
      title: 'دفع آمن',
      description: 'طرق دفع متعددة وآمنة 100%',
      isActive: true
    },
    {
      id: 3,
      icon: 'Shield',
      title: 'ضمان الجودة',
      description: 'ضمان استرداد المال خلال 30 يوم',
      isActive: true
    },
    {
      id: 4,
      icon: 'Star',
      title: 'خدمة عملاء ممتازة',
      description: 'دعم فني على مدار 24/7',
      isActive: true
    }
  ],
  paymentSettings: {
    enableCashOnDelivery: true,
    enableCreditCard: true,
    enablePayPal: false,
    enableBankTransfer: true,
    freeShippingThreshold: 500,
    shippingCost: 50,
    taxRate: 14
  },
  updatedAt: new Date()
};

// الفئات الافتراضية
const defaultCategories = [
  { name: 'أدوات تجميل', isActive: true, createdAt: new Date() },
  { name: 'ملابس نسائية', isActive: true, createdAt: new Date() },
  { name: 'ملابس رجالية', isActive: true, createdAt: new Date() },
  { name: 'شامبوهات ومنتجات الشعر', isActive: true, createdAt: new Date() },
  { name: 'عطور ومستحضرات', isActive: true, createdAt: new Date() },
  { name: 'أحذية', isActive: true, createdAt: new Date() },
  { name: 'حقائب ومحافظ', isActive: true, createdAt: new Date() },
  { name: 'إكسسوارات', isActive: true, createdAt: new Date() },
  { name: 'منتجات العناية بالبشرة', isActive: true, createdAt: new Date() },
  { name: 'مكياج', isActive: true, createdAt: new Date() },
  { name: 'منتجات الأطفال', isActive: true, createdAt: new Date() },
  { name: 'منتجات رياضية', isActive: true, createdAt: new Date() }
];

// وظيفة إنشاء حساب المدير الأول
export const createFirstAdmin = async (email: string, password: string, name: string) => {
  try {
    console.log('👤 إنشاء حساب المدير الأول...');

    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // إنشاء ملف المستخدم في Firestore
    await setDoc(doc(db, 'users', user.uid), {
      name,
      email,
      role: 'admin',
      isActive: true,
      createdAt: new Date(),
    });

    console.log('✅ تم إنشاء حساب المدير الأول');
    return { success: true, uid: user.uid };
  } catch (error: any) {
    console.error('❌ خطأ في إنشاء حساب المدير:', error);
    return { success: false, error: error.message };
  }
};

// وظيفة تهيئة Firestore
export const initializeFirestore = async () => {
  try {
    console.log('🔥 بدء تهيئة Firestore...');

    // إنشاء إعدادات المتجر
    await setDoc(doc(db, 'settings', 'store'), defaultStoreSettings);
    console.log('✅ تم إنشاء إعدادات المتجر');

    // إنشاء الفئات الافتراضية
    const categoriesPromises = defaultCategories.map(category =>
      addDoc(collection(db, 'categories'), category)
    );
    await Promise.all(categoriesPromises);
    console.log('✅ تم إنشاء الفئات الافتراضية');

    console.log('🎉 تم تهيئة Firestore بنجاح!');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تهيئة Firestore:', error);
    return false;
  }
};

// وظيفة التحقق من حالة Firestore
export const checkFirestoreConnection = async () => {
  try {
    // محاولة قراءة إعدادات المتجر
    const settingsRef = doc(db, 'settings', 'store');
    console.log('🔍 التحقق من اتصال Firestore...');
    console.log('✅ Firestore متصل بنجاح!');
    return true;
  } catch (error) {
    console.error('❌ فشل الاتصال بـ Firestore:', error);
    return false;
  }
};
