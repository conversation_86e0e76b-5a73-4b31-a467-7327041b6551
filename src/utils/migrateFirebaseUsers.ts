import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// وظيفة لإضافة مستخدم موجود في Firebase Auth إلى Firestore
export const addExistingUserToFirestore = async (
  uid: string, 
  email: string, 
  name: string, 
  role: 'admin' | 'merchant' | 'customer' = 'admin'
) => {
  try {
    console.log(`🔄 إضافة المستخدم ${email} إلى Firestore...`);
    
    // التحقق من وجود المستخدم مسبقاً
    const userDoc = await getDoc(doc(db, 'users', uid));
    if (userDoc.exists()) {
      console.log('✅ المستخدم موجود بالفعل في Firestore');
      return { success: true, message: 'المستخدم موجود بالفعل' };
    }
    
    // إنشاء ملف المستخدم في Firestore
    await setDoc(doc(db, 'users', uid), {
      name,
      email,
      role,
      isActive: true,
      createdAt: new Date(),
      avatar: null,
    });
    
    console.log('✅ تم إضافة المستخدم بنجاح إلى Firestore');
    return { success: true, message: 'تم إضافة المستخدم بنجاح' };
    
  } catch (error) {
    console.error('❌ خطأ في إضافة المستخدم:', error);
    return { success: false, error: error };
  }
};

// وظيفة لإضافة مستخدم بالإيميل (للمستخدمين الموجودين في Firebase Auth)
export const addUserByEmail = async (
  email: string, 
  name: string, 
  role: 'admin' | 'merchant' | 'customer' = 'admin'
) => {
  try {
    console.log(`🔍 البحث عن المستخدم ${email} في Firebase Auth...`);
    
    // ملاحظة: هذه الوظيفة تتطلب Firebase Admin SDK للبحث عن المستخدمين
    // في الوقت الحالي، سنستخدم طريقة أخرى
    
    console.log('💡 يرجى تسجيل الدخول بالإيميل أولاً لإنشاء ملف المستخدم تلقائياً');
    return { 
      success: false, 
      message: 'يرجى تسجيل الدخول بالإيميل أولاً لإنشاء ملف المستخدم تلقائياً' 
    };
    
  } catch (error) {
    console.error('❌ خطأ في البحث عن المستخدم:', error);
    return { success: false, error: error };
  }
};

// قائمة المستخدمين المدراء المعتمدين
export const approvedAdminEmails = [
  '<EMAIL>',
  // أضف إيميلات المدراء المعتمدين هنا
];

// وظيفة للتحقق من صلاحيات المدير
export const isApprovedAdmin = (email: string): boolean => {
  return approvedAdminEmails.includes(email) || email.includes('admin');
};

// وظيفة لتحديث دور مستخدم موجود
export const updateUserRole = async (
  uid: string, 
  newRole: 'admin' | 'merchant' | 'customer'
) => {
  try {
    console.log(`🔄 تحديث دور المستخدم ${uid} إلى ${newRole}...`);
    
    const userRef = doc(db, 'users', uid);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return { success: false, message: 'المستخدم غير موجود في Firestore' };
    }
    
    await setDoc(userRef, { role: newRole }, { merge: true });
    
    console.log('✅ تم تحديث دور المستخدم بنجاح');
    return { success: true, message: 'تم تحديث دور المستخدم بنجاح' };
    
  } catch (error) {
    console.error('❌ خطأ في تحديث دور المستخدم:', error);
    return { success: false, error: error };
  }
};
