import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import AccountSettings from '@/components/AccountSettings';

const CustomerAccount = () => {
  const { state } = useAuth();

  // Redirect if not authenticated
  if (!state.isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <AccountSettings />
        </div>
      </div>
    </div>
  );
};

export default CustomerAccount;
