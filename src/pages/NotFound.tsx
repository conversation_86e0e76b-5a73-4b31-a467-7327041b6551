import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import Header from "@/components/Header";
import { Home } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      <Header />
      <div className="container mx-auto px-4 py-16 flex items-center justify-center">
        <div className="text-center bg-white dark:bg-gray-900 rounded-2xl p-12 shadow-xl max-w-md mx-auto">
          <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">404</h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">عذراً! الصفحة غير موجودة</p>
          <p className="text-gray-500 dark:text-gray-500 mb-8">الصفحة التي تبحث عنها غير متوفرة أو تم نقلها</p>
          <Link to="/">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-xl px-8 py-3">
              <Home className="mr-2 h-4 w-4" />
              العودة للرئيسية
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
