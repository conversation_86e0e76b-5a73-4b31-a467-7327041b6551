import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Settings, Store, Mail, Phone, MapPin, Save } from 'lucide-react';

const AdminSettings = () => {
  const { toast } = useToast();
  const [settings, setSettings] = useState({
    storeName: 'Easy Shop',
    storeDescription: 'متجرك الإلكتروني المفضل للتقنية',
    contactEmail: '<EMAIL>',
    contactPhone: '+20 ************',
    address: 'القاهرة، مصر',
    currency: 'ج.م',
    taxRate: '14',
    shippingFee: '50',
    freeShippingThreshold: '500',
    enableNotifications: true,
    enableReviews: true,
    enableWishlist: true,
    maintenanceMode: false,
  });

  const handleChange = (field: string, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    // Here you would typically save to a backend
    toast({
      title: "تم حفظ الإعدادات",
      description: "تم حفظ جميع الإعدادات بنجاح",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          إعدادات المتجر
        </h1>
        <Button onClick={handleSave} className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700">
          <Save className="mr-2 h-4 w-4" />
          حفظ الإعدادات
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Store Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Store className="h-5 w-5" />
              معلومات المتجر
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                اسم المتجر
              </label>
              <Input
                value={settings.storeName}
                onChange={(e) => handleChange('storeName', e.target.value)}
                className="text-right"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                وصف المتجر
              </label>
              <Textarea
                value={settings.storeDescription}
                onChange={(e) => handleChange('storeDescription', e.target.value)}
                className="text-right"
                rows={3}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                العملة
              </label>
              <Input
                value={settings.currency}
                onChange={(e) => handleChange('currency', e.target.value)}
                className="text-right"
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              معلومات التواصل
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                البريد الإلكتروني
              </label>
              <Input
                type="email"
                value={settings.contactEmail}
                onChange={(e) => handleChange('contactEmail', e.target.value)}
                className="text-right"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                رقم الهاتف
              </label>
              <Input
                value={settings.contactPhone}
                onChange={(e) => handleChange('contactPhone', e.target.value)}
                className="text-right"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                العنوان
              </label>
              <Textarea
                value={settings.address}
                onChange={(e) => handleChange('address', e.target.value)}
                className="text-right"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Financial Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              الإعدادات المالية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                معدل الضريبة (%)
              </label>
              <Input
                type="number"
                value={settings.taxRate}
                onChange={(e) => handleChange('taxRate', e.target.value)}
                className="text-right"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                رسوم الشحن (ج.م)
              </label>
              <Input
                type="number"
                value={settings.shippingFee}
                onChange={(e) => handleChange('shippingFee', e.target.value)}
                className="text-right"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-right">
                الحد الأدنى للشحن المجاني (ج.م)
              </label>
              <Input
                type="number"
                value={settings.freeShippingThreshold}
                onChange={(e) => handleChange('freeShippingThreshold', e.target.value)}
                className="text-right"
              />
            </div>
          </CardContent>
        </Card>

        {/* Feature Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              إعدادات المميزات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Switch
                checked={settings.enableNotifications}
                onCheckedChange={(checked) => handleChange('enableNotifications', checked)}
              />
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                تفعيل الإشعارات
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <Switch
                checked={settings.enableReviews}
                onCheckedChange={(checked) => handleChange('enableReviews', checked)}
              />
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                تفعيل التقييمات
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <Switch
                checked={settings.enableWishlist}
                onCheckedChange={(checked) => handleChange('enableWishlist', checked)}
              />
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                تفعيل قائمة الأمنيات
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <Switch
                checked={settings.maintenanceMode}
                onCheckedChange={(checked) => handleChange('maintenanceMode', checked)}
              />
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                وضع الصيانة
              </label>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle>معلومات النظام</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="font-semibold text-gray-900 dark:text-white">إصدار النظام</div>
              <div className="text-gray-600 dark:text-gray-400">v1.0.0</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="font-semibold text-gray-900 dark:text-white">آخر تحديث</div>
              <div className="text-gray-600 dark:text-gray-400">{new Date().toLocaleDateString('ar-EG')}</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="font-semibold text-gray-900 dark:text-white">حالة النظام</div>
              <div className="text-green-600 dark:text-green-400">يعمل بشكل طبيعي</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminSettings;
