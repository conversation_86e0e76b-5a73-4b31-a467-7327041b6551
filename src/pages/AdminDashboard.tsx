import { useState } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProducts } from '@/contexts/ProductsContext';
import { useAds } from '@/contexts/AdsContext';
import Header from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Package, Users, Megaphone, BarChart3, Plus, Settings, ArrowLeft } from 'lucide-react';
import AdminProducts from '@/components/admin/AdminProducts';
import AdminAds from '@/components/admin/AdminAds';
import AdminUsers from '@/components/admin/AdminUsers';
import AdminSettings from '@/components/admin/AdminSettings';
import AdminChat from '@/components/AdminChat';

const AdminDashboard = () => {
  const { state } = useAuth();
  const { state: productsState } = useProducts();
  const { state: adsState } = useAds();
  const [activeSection, setActiveSection] = useState('dashboard');

  // Redirect if not admin
  if (!state.isAuthenticated || state.user?.role !== 'admin') {
    return <Navigate to="/login" replace />;
  }

  const menuItems = [
    { id: 'dashboard', name: 'لوحة التحكم', icon: BarChart3 },
    { id: 'products', name: 'إدارة المنتجات', icon: Package },
    { id: 'ads', name: 'إدارة الإعلانات', icon: Megaphone },
    { id: 'users', name: 'إدارة المستخدمين', icon: Users },
    { id: 'settings', name: 'الإعدادات', icon: Settings },
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'products':
        return <AdminProducts />;
      case 'ads':
        return <AdminAds />;
      case 'users':
        return <AdminUsers />;
      case 'settings':
        return <AdminSettings />;
      default:
        return <DashboardOverview />;
    }
  };

  const DashboardOverview = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المنتجات</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{productsState.products.length}</div>
            <p className="text-xs text-muted-foreground">منتج</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الإعلانات النشطة</CardTitle>
            <Megaphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{adsState.activeAds.length}</div>
            <p className="text-xs text-muted-foreground">إعلان نشط</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المستخدمين</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">مستخدم مسجل</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المبيعات</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">طلب هذا الشهر</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>إجراءات سريعة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={() => setActiveSection('products')}
              className="h-20 flex flex-col items-center justify-center space-y-2"
            >
              <Plus className="h-6 w-6" />
              <span>إضافة منتج جديد</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => setActiveSection('ads')}
              className="h-20 flex flex-col items-center justify-center space-y-2"
            >
              <Megaphone className="h-6 w-6" />
              <span>إضافة إعلان جديد</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => setActiveSection('users')}
              className="h-20 flex flex-col items-center justify-center space-y-2"
            >
              <Users className="h-6 w-6" />
              <span>إدارة المستخدمين</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              {activeSection !== 'dashboard' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setActiveSection('dashboard')}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  العودة
                </Button>
              )}
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {menuItems.find(item => item.id === activeSection)?.name || 'لوحة تحكم المدير'}
              </h1>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              مرحباً، {state.user?.name}
            </div>
          </div>

          {/* Navigation Menu */}
          {activeSection === 'dashboard' && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              {menuItems.slice(1).map((item) => {
                const Icon = item.icon;
                return (
                  <Card
                    key={item.id}
                    className="cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() => setActiveSection(item.id)}
                  >
                    <CardContent className="flex flex-col items-center justify-center p-6">
                      <Icon className="h-8 w-8 text-blue-600 mb-2" />
                      <span className="font-semibold text-center">{item.name}</span>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          {/* Content */}
          {renderContent()}

        </div>
      </div>

      {/* Admin Chat */}
      <AdminChat />
    </div>
  );
};

export default AdminDashboard;
