
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import Header from '@/components/Header';
import { useProducts } from '@/contexts/ProductsContext';
import { useCart } from '@/contexts/CartContext';
import { useToast } from '@/hooks/use-toast';
import { ArrowRight, ShoppingCart, Star } from 'lucide-react';

const ProductDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { state } = useProducts();
  const { dispatch } = useCart();
  const { toast } = useToast();

  const product = state.products.find(p => p.id === Number(id) && p.isActive);

  if (!product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <div className="bg-white dark:bg-gray-900 rounded-2xl p-12 shadow-xl max-w-md mx-auto">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">المنتج غير موجود</h1>
            <Link to="/products">
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-xl">
                العودة إلى المنتجات
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const handleAddToCart = () => {
    dispatch({ type: 'ADD_TO_CART', product });
    toast({
      title: "تم إضافة المنتج",
      description: `تم إضافة ${product.name} إلى السلة بنجاح`,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <Button
          variant="outline"
          onClick={() => navigate(-1)}
          className="mb-6 flex items-center gap-2 rounded-xl border-2 hover:scale-105 transition-all duration-300"
        >
          <ArrowRight className="h-4 w-4" />
          رجوع
        </Button>

        <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
            {/* Product Image */}
            <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-2xl overflow-hidden shadow-lg">
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
              />
            </div>

            {/* Product Info */}
            <div className="flex flex-col justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 text-right">
                  {product.name}
                </h1>
                
                <div className="flex items-center justify-end mb-6">
                  <div className="flex items-center text-yellow-500 mr-2">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-current" />
                    ))}
                  </div>
                  <span className="text-gray-600 dark:text-gray-400">(تقييم ممتاز)</span>
                </div>
                
                <p className="text-4xl font-bold mb-6 text-right">
                  <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                    {product.price} جنيه مصري
                  </span>
                </p>
                
                <div className="prose prose-lg text-right mb-8">
                  <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">وصف المنتج:</h3>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed text-lg">
                    {product.description}
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col gap-4">
                <Button
                  size="lg"
                  onClick={handleAddToCart}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 text-lg py-4"
                >
                  <ShoppingCart className="mr-2 h-5 w-5" />
                  أضف إلى السلة - {product.price} ج.م
                </Button>
                
                <Link to="/products">
                  <Button variant="outline" size="lg" className="w-full rounded-xl border-2 hover:scale-105 transition-all duration-300">
                    رجوع إلى المنتجات
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetails;
