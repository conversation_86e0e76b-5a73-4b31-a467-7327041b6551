import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProducts } from '@/contexts/ProductsContext';
import Header from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Package, TrendingUp, BarChart3, ShoppingCart } from 'lucide-react';

const MerchantDashboard = () => {
  const { state } = useAuth();
  const { getProductsByMerchant } = useProducts();

  // Redirect if not merchant
  if (!state.isAuthenticated || state.user?.role !== 'merchant') {
    return <Navigate to="/login" replace />;
  }

  const merchantProducts = getProductsByMerchant(state.user?.id || 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              لوحة تحكم التاجر
            </h1>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              مرحباً، {state.user?.name}
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">منتجاتي</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{merchantProducts.length}</div>
                <p className="text-xs text-muted-foreground">منتج نشط</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي المخزون</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {merchantProducts.reduce((total, product) => total + product.stock, 0)}
                </div>
                <p className="text-xs text-muted-foreground">قطعة متوفرة</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">قيمة المخزون</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {merchantProducts.reduce((total, product) => total + (product.price * product.stock), 0).toLocaleString()} ج.م
                </div>
                <p className="text-xs text-muted-foreground">إجمالي قيمة المخزون</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المبيعات</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">طلب هذا الشهر</p>
              </CardContent>
            </Card>
          </div>

          {/* Welcome Message */}
          <Card>
            <CardHeader>
              <CardTitle>مرحباً بك في لوحة تحكم التاجر</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  إدارة متجرك الإلكتروني
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  يمكنك من هنا إدارة منتجاتك ومراقبة المبيعات والتواصل مع الإدارة
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                    <Package className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100">إدارة المنتجات</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-200">إضافة وتعديل منتجاتك</p>
                  </div>
                  <div className="p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                    <BarChart3 className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <h4 className="font-semibold text-green-900 dark:text-green-100">التقارير</h4>
                    <p className="text-sm text-green-700 dark:text-green-200">مراقبة الأداء والمبيعات</p>
                  </div>
                  <div className="p-4 bg-purple-50 dark:bg-purple-900 rounded-lg">
                    <ShoppingCart className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <h4 className="font-semibold text-purple-900 dark:text-purple-100">المخزون</h4>
                    <p className="text-sm text-purple-700 dark:text-purple-200">إدارة المخزون والكميات</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Products */}
          {merchantProducts.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>منتجاتي الحديثة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {merchantProducts.slice(0, 3).map((product) => (
                    <div key={product.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-12 h-12 object-cover rounded-lg"
                        />
                        <div className="text-right">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {product.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {product.category}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-900 dark:text-white">
                          {product.price} ج.م
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          المخزون: {product.stock}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default MerchantDashboard;
