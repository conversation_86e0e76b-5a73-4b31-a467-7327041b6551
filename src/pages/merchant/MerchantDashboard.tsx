import { useState } from 'react';
import { Routes, Route, Link, useLocation, Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProducts } from '@/contexts/ProductsContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  LayoutDashboard, 
  Package, 
  BarChart3,
  Store,
  Menu,
  X,
  Plus,
  TrendingUp,
  ShoppingCart
} from 'lucide-react';
import MerchantProducts from './MerchantProducts';

const MerchantDashboard = () => {
  const { state } = useAuth();
  const { getProductsByMerchant } = useProducts();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Redirect if not merchant
  if (!state.isAuthenticated || state.user?.role !== 'merchant') {
    return <Navigate to="/login" replace />;
  }

  const merchantProducts = getProductsByMerchant(state.user?.id || 0);

  const menuItems = [
    { name: 'لوحة التحكم', path: '/merchant', icon: LayoutDashboard },
    { name: 'إدارة المنتجات', path: '/merchant/products', icon: Package },
    { name: 'التقارير', path: '/merchant/reports', icon: BarChart3 },
  ];

  const DashboardOverview = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          لوحة تحكم التاجر
        </h1>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          مرحباً، {state.user?.name}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">منتجاتي</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{merchantProducts.length}</div>
            <p className="text-xs text-muted-foreground">منتج نشط</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المخزون</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {merchantProducts.reduce((total, product) => total + product.stock, 0)}
            </div>
            <p className="text-xs text-muted-foreground">قطعة متوفرة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">قيمة المخزون</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {merchantProducts.reduce((total, product) => total + (product.price * product.stock), 0).toLocaleString()} ج.م
            </div>
            <p className="text-xs text-muted-foreground">إجمالي قيمة المخزون</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المبيعات</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">طلب هذا الشهر</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>إجراءات سريعة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/merchant/products">
              <Button className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                <Plus className="h-6 w-6" />
                <span>إضافة منتج جديد</span>
              </Button>
            </Link>
            <Link to="/merchant/products">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                <Package className="h-6 w-6" />
                <span>إدارة المخزون</span>
              </Button>
            </Link>
            <Link to="/merchant/reports">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                <BarChart3 className="h-6 w-6" />
                <span>عرض التقارير</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Recent Products */}
      <Card>
        <CardHeader>
          <CardTitle>المنتجات الحديثة</CardTitle>
        </CardHeader>
        <CardContent>
          {merchantProducts.length > 0 ? (
            <div className="space-y-4">
              {merchantProducts.slice(0, 5).map((product) => (
                <div key={product.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-12 h-12 object-cover rounded-lg"
                    />
                    <div className="text-right">
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {product.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {product.category}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {product.price} ج.م
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      المخزون: {product.stock}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                لا توجد منتجات
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                ابدأ بإضافة منتجاتك الأولى
              </p>
              <Link to="/merchant/products">
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  إضافة منتج
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  const ReportsPage = () => (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
        التقارير والإحصائيات
      </h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>تقرير المبيعات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                لا توجد مبيعات حتى الآن
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>تقرير المخزون</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span>إجمالي المنتجات:</span>
                <span className="font-semibold">{merchantProducts.length}</span>
              </div>
              <div className="flex justify-between">
                <span>إجمالي المخزون:</span>
                <span className="font-semibold">
                  {merchantProducts.reduce((total, product) => total + product.stock, 0)} قطعة
                </span>
              </div>
              <div className="flex justify-between">
                <span>قيمة المخزون:</span>
                <span className="font-semibold">
                  {merchantProducts.reduce((total, product) => total + (product.price * product.stock), 0).toLocaleString()} ج.م
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="bg-white dark:bg-gray-800"
        >
          {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Sidebar */}
      <div className={`fixed inset-y-0 right-0 z-40 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex items-center justify-center h-16 border-b border-gray-200 dark:border-gray-700">
          <Link to="/" className="flex items-center space-x-2 space-x-reverse">
            <Store className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900 dark:text-white">Easy Shop</span>
          </Link>
        </div>
        
        <nav className="mt-8">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                  isActive
                    ? 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400 border-r-2 border-blue-600'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <Icon className="ml-3 h-5 w-5" />
                {item.name}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Main content */}
      <div className="lg:mr-64">
        <div className="p-6">
          <Routes>
            <Route path="/" element={<DashboardOverview />} />
            <Route path="/products" element={<MerchantProducts />} />
            <Route path="/reports" element={<ReportsPage />} />
          </Routes>
        </div>
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default MerchantDashboard;
