import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  onSnapshot,
  setDoc
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';

// أنواع البيانات
export interface Product {
  id: string;
  name: string;
  price: number;
  description: string;
  image: string;
  category: string;
  stock: number;
  isActive: boolean;
  merchantId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Ad {
  id: string;
  title: string;
  description: string;
  image: string;
  link?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'merchant' | 'customer';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface StoreSettings {
  id: string;
  storeName: string;
  storeDescription: string;
  storeEmail: string;
  storePhone: string;
  storeAddress: string;
  currency: string;
  language: string;
  timezone: string;
  logo?: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  features: Array<{
    id: number;
    icon: string;
    title: string;
    description: string;
    isActive: boolean;
  }>;
  paymentSettings: {
    enableCashOnDelivery: boolean;
    enableCreditCard: boolean;
    enablePayPal: boolean;
    enableBankTransfer: boolean;
    freeShippingThreshold: number;
    shippingCost: number;
    taxRate: number;
  };
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  isActive: boolean;
  createdAt: Date;
}

// خدمات المنتجات
export const productService = {
  // جلب جميع المنتجات
  async getAll(): Promise<Product[]> {
    const querySnapshot = await getDocs(collection(db, 'products'));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate(),
    })) as Product[];
  },

  // جلب منتجات تاجر معين
  async getByMerchant(merchantId: string): Promise<Product[]> {
    const q = query(
      collection(db, 'products'), 
      where('merchantId', '==', merchantId)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate(),
    })) as Product[];
  },

  // إضافة منتج جديد
  async add(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'products'), {
      ...product,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return docRef.id;
  },

  // تحديث منتج
  async update(id: string, product: Partial<Product>): Promise<void> {
    const docRef = doc(db, 'products', id);
    await updateDoc(docRef, {
      ...product,
      updatedAt: new Date(),
    });
  },

  // حذف منتج
  async delete(id: string): Promise<void> {
    await deleteDoc(doc(db, 'products', id));
  },

  // الاستماع للتغييرات
  onSnapshot(callback: (products: Product[]) => void) {
    return onSnapshot(collection(db, 'products'), (snapshot) => {
      const products = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate(),
      })) as Product[];
      callback(products);
    });
  }
};

// خدمات الإعلانات
export const adService = {
  async getAll(): Promise<Ad[]> {
    const querySnapshot = await getDocs(collection(db, 'ads'));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate(),
    })) as Ad[];
  },

  async add(ad: Omit<Ad, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'ads'), {
      ...ad,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return docRef.id;
  },

  async update(id: string, ad: Partial<Ad>): Promise<void> {
    const docRef = doc(db, 'ads', id);
    await updateDoc(docRef, {
      ...ad,
      updatedAt: new Date(),
    });
  },

  async delete(id: string): Promise<void> {
    await deleteDoc(doc(db, 'ads', id));
  },

  onSnapshot(callback: (ads: Ad[]) => void) {
    return onSnapshot(collection(db, 'ads'), (snapshot) => {
      const ads = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate(),
      })) as Ad[];
      callback(ads);
    });
  }
};

// خدمات المستخدمين
export const userService = {
  async getAll(): Promise<User[]> {
    const querySnapshot = await getDocs(collection(db, 'users'));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate(),
    })) as User[];
  },

  async getById(id: string): Promise<User | null> {
    const docRef = doc(db, 'users', id);
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data(),
        createdAt: docSnap.data().createdAt?.toDate(),
        updatedAt: docSnap.data().updatedAt?.toDate(),
      } as User;
    }
    return null;
  },

  async add(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'users'), {
      ...user,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return docRef.id;
  },

  async update(id: string, user: Partial<User>): Promise<void> {
    const docRef = doc(db, 'users', id);
    await updateDoc(docRef, {
      ...user,
      updatedAt: new Date(),
    });
  }
};

// خدمات إعدادات المتجر
export const settingsService = {
  async get(): Promise<StoreSettings | null> {
    const docRef = doc(db, 'settings', 'store');
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data(),
        updatedAt: docSnap.data().updatedAt?.toDate(),
      } as StoreSettings;
    }
    return null;
  },

  async update(settings: Partial<StoreSettings>): Promise<void> {
    const docRef = doc(db, 'settings', 'store');
    await setDoc(docRef, {
      ...settings,
      updatedAt: new Date(),
    }, { merge: true });
  },

  onSnapshot(callback: (settings: StoreSettings | null) => void) {
    return onSnapshot(doc(db, 'settings', 'store'), (doc) => {
      if (doc.exists()) {
        const settings = {
          id: doc.id,
          ...doc.data(),
          updatedAt: doc.data().updatedAt?.toDate(),
        } as StoreSettings;
        callback(settings);
      } else {
        callback(null);
      }
    });
  }
};

// خدمات الفئات
export const categoryService = {
  async getAll(): Promise<Category[]> {
    const querySnapshot = await getDocs(collection(db, 'categories'));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
    })) as Category[];
  },

  async add(category: Omit<Category, 'id' | 'createdAt'>): Promise<string> {
    const docRef = await addDoc(collection(db, 'categories'), {
      ...category,
      createdAt: new Date(),
    });
    return docRef.id;
  },

  async update(id: string, category: Partial<Category>): Promise<void> {
    const docRef = doc(db, 'categories', id);
    await updateDoc(docRef, category);
  },

  async delete(id: string): Promise<void> {
    await deleteDoc(doc(db, 'categories', id));
  }
};

// خدمات رفع الصور
export const imageService = {
  async upload(file: File, path: string): Promise<string> {
    const storageRef = ref(storage, path);
    const snapshot = await uploadBytes(storageRef, file);
    return await getDownloadURL(snapshot.ref);
  },

  async delete(url: string): Promise<void> {
    const storageRef = ref(storage, url);
    await deleteObject(storageRef);
  }
};
