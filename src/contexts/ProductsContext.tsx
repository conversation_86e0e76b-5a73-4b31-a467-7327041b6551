import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { productService, Product } from '@/services/firebaseService';

// إعادة تصدير نوع Product من الخدمة
export type { Product } from '@/services/firebaseService';

interface ProductsState {
  products: Product[];
  categories: string[];
  loading: boolean;
  error: string | null;
}

type ProductsAction =
  | { type: 'SET_LOADING'; loading: boolean }
  | { type: 'SET_ERROR'; error: string | null }
  | { type: 'SET_PRODUCTS'; products: Product[] }
  | { type: 'ADD_PRODUCT'; product: Product }
  | { type: 'UPDATE_PRODUCT'; product: Product }
  | { type: 'DELETE_PRODUCT'; productId: string }
  | { type: 'SET_CATEGORIES'; categories: string[] };

const ProductsContext = createContext<{
  state: ProductsState;
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateProduct: (id: string, product: Partial<Product>) => Promise<void>;
  deleteProduct: (productId: string) => Promise<void>;
  toggleProductStatus: (productId: string) => Promise<void>;
  getProductsByMerchant: (merchantId: string) => Product[];
  refreshProducts: () => Promise<void>;
} | null>(null);

const productsReducer = (state: ProductsState, action: ProductsAction): ProductsState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.loading };
    case 'SET_ERROR':
      return { ...state, error: action.error, loading: false };
    case 'SET_PRODUCTS':
      const categories = [...new Set(action.products.map(p => p.category))];
      return {
        ...state,
        products: action.products,
        categories,
        loading: false,
        error: null,
      };
    case 'ADD_PRODUCT':
      const newProducts = [...state.products, action.product];
      const newCategories = [...new Set(newProducts.map(p => p.category))];
      return {
        ...state,
        products: newProducts,
        categories: newCategories,
      };
    case 'UPDATE_PRODUCT':
      const updatedProducts = state.products.map(product =>
        product.id === action.product.id ? action.product : product
      );
      const updatedCategories = [...new Set(updatedProducts.map(p => p.category))];
      return {
        ...state,
        products: updatedProducts,
        categories: updatedCategories,
      };
    case 'DELETE_PRODUCT':
      const filteredProducts = state.products.filter(product => product.id !== action.productId);
      const filteredCategories = [...new Set(filteredProducts.map(p => p.category))];
      return {
        ...state,
        products: filteredProducts,
        categories: filteredCategories,
      };
    case 'SET_CATEGORIES':
      return { ...state, categories: action.categories };
    default:
      return state;
  }
};

// الحالة الأولية للمنتجات (فارغة)
const initialState: ProductsState = {
  products: [],
  categories: [],
  loading: false,
  error: null,
};

export const ProductsProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(productsReducer, initialState);

  // تحميل المنتجات عند بدء التطبيق
  useEffect(() => {
    refreshProducts();
  }, []);

  const refreshProducts = async () => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      const products = await productService.getAll();
      dispatch({ type: 'SET_PRODUCTS', products });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', error: 'فشل في تحميل المنتجات' });
      console.error('Error loading products:', error);
    }
  };

  const addProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      const id = await productService.add(productData);
      const newProduct: Product = {
        ...productData,
        id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      dispatch({ type: 'ADD_PRODUCT', product: newProduct });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', error: 'فشل في إضافة المنتج' });
      console.error('Error adding product:', error);
      throw error;
    }
  };

  const updateProduct = async (id: string, productData: Partial<Product>) => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      await productService.update(id, productData);
      const updatedProduct = {
        ...state.products.find(p => p.id === id)!,
        ...productData,
        updatedAt: new Date(),
      };
      dispatch({ type: 'UPDATE_PRODUCT', product: updatedProduct });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', error: 'فشل في تحديث المنتج' });
      console.error('Error updating product:', error);
      throw error;
    }
  };

  const deleteProduct = async (productId: string) => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      await productService.delete(productId);
      dispatch({ type: 'DELETE_PRODUCT', productId });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', error: 'فشل في حذف المنتج' });
      console.error('Error deleting product:', error);
      throw error;
    }
  };

  const toggleProductStatus = async (productId: string) => {
    try {
      const product = state.products.find(p => p.id === productId);
      if (product) {
        await updateProduct(productId, { isActive: !product.isActive });
      }
    } catch (error) {
      console.error('Error toggling product status:', error);
      throw error;
    }
  };

  const getProductsByMerchant = (merchantId: string) => {
    return state.products.filter(product => product.merchantId === merchantId);
  };

  return (
    <ProductsContext.Provider value={{
      state,
      addProduct,
      updateProduct,
      deleteProduct,
      toggleProductStatus,
      getProductsByMerchant,
      refreshProducts,
    }}>
      {children}
    </ProductsContext.Provider>
  );
};

export const useProducts = () => {
  const context = useContext(ProductsContext);
  if (!context) {
    throw new Error('useProducts must be used within a ProductsProvider');
  }
  return context;
};
