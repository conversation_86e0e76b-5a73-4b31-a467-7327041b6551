import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Product } from './CartContext';

export interface ExtendedProduct extends Product {
  category: string;
  stock: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  merchantId?: number;
}

interface ProductsState {
  products: ExtendedProduct[];
  categories: string[];
}

type ProductsAction = 
  | { type: 'SET_PRODUCTS'; products: ExtendedProduct[] }
  | { type: 'ADD_PRODUCT'; product: ExtendedProduct }
  | { type: 'UPDATE_PRODUCT'; product: ExtendedProduct }
  | { type: 'DELETE_PRODUCT'; productId: number }
  | { type: 'TOGGLE_PRODUCT_STATUS'; productId: number }
  | { type: 'UPDATE_STOCK'; productId: number; stock: number };

const ProductsContext = createContext<{
  state: ProductsState;
  dispatch: React.Dispatch<ProductsAction>;
  addProduct: (product: Omit<ExtendedProduct, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProduct: (product: ExtendedProduct) => void;
  deleteProduct: (productId: number) => void;
  toggleProductStatus: (productId: number) => void;
  updateStock: (productId: number, stock: number) => void;
  getProductsByMerchant: (merchantId: number) => ExtendedProduct[];
} | null>(null);

const productsReducer = (state: ProductsState, action: ProductsAction): ProductsState => {
  switch (action.type) {
    case 'SET_PRODUCTS':
      const categories = [...new Set(action.products.map(p => p.category))];
      return {
        ...state,
        products: action.products,
        categories,
      };
    case 'ADD_PRODUCT':
      const newProducts = [...state.products, action.product];
      const newCategories = [...new Set(newProducts.map(p => p.category))];
      return {
        ...state,
        products: newProducts,
        categories: newCategories,
      };
    case 'UPDATE_PRODUCT':
      const updatedProducts = state.products.map(product => 
        product.id === action.product.id ? action.product : product
      );
      const updatedCategories = [...new Set(updatedProducts.map(p => p.category))];
      return {
        ...state,
        products: updatedProducts,
        categories: updatedCategories,
      };
    case 'DELETE_PRODUCT':
      const filteredProducts = state.products.filter(product => product.id !== action.productId);
      const filteredCategories = [...new Set(filteredProducts.map(p => p.category))];
      return {
        ...state,
        products: filteredProducts,
        categories: filteredCategories,
      };
    case 'TOGGLE_PRODUCT_STATUS':
      const toggledProducts = state.products.map(product => 
        product.id === action.productId 
          ? { ...product, isActive: !product.isActive, updatedAt: new Date() }
          : product
      );
      return {
        ...state,
        products: toggledProducts,
      };
    case 'UPDATE_STOCK':
      const stockUpdatedProducts = state.products.map(product => 
        product.id === action.productId 
          ? { ...product, stock: action.stock, updatedAt: new Date() }
          : product
      );
      return {
        ...state,
        products: stockUpdatedProducts,
      };
    default:
      return state;
  }
};

// Convert existing products to extended format
const mockExtendedProducts: ExtendedProduct[] = [
  {
    id: 1,
    name: "لابتوب Dell XPS 13",
    price: 45000,
    image: "https://images.unsplash.com/photo-1588160702438-9b02ab6515c9?w=500&h=500&fit=crop",
    description: "لابتوب Dell XPS 13 بمعالج Intel Core i7 وذاكرة 16GB RAM و SSD 512GB. مثالي للعمل والدراسة مع شاشة عالية الدقة وتصميم أنيق.",
    category: "أجهزة كمبيوتر",
    stock: 15,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    merchantId: 2,
  },
  {
    id: 2,
    name: "سماعات لاسلكية",
    price: 2990,
    image: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=500&h=500&fit=crop",
    description: "سماعات لاسلكية عالية الجودة مع تقنية إلغاء الضوضاء وبطارية تدوم 30 ساعة. صوت نقي وتصميم مريح.",
    category: "إكسسوارات",
    stock: 25,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    merchantId: 2,
  },
  {
    id: 3,
    name: "ساعة ذكية Apple Watch",
    price: 12000,
    image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=500&h=500&fit=crop",
    description: "ساعة Apple Watch Series 9 مع مستشعرات صحية متقدمة ومقاومة للماء. تتبع النشاط والصحة مع تطبيقات متنوعة.",
    category: "إكسسوارات",
    stock: 10,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    merchantId: 2,
  },
  {
    id: 4,
    name: "كاميرا Canon EOS R6",
    price: 85000,
    image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=500&h=500&fit=crop",
    description: "كاميرا Canon EOS R6 الاحترافية مع دقة 20 ميجابكسل وتصوير فيديو 4K. مثالية للمصورين المحترفين.",
    category: "كاميرات",
    stock: 5,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    merchantId: 2,
  },
  {
    id: 5,
    name: "هاتف iPhone 15 Pro",
    price: 42000,
    image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=500&h=500&fit=crop",
    description: "iPhone 15 Pro مع معالج A17 Pro وكاميرا ثلاثية متطورة. أداء استثنائي وتصميم من التيتانيوم.",
    category: "هواتف ذكية",
    stock: 20,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    merchantId: 2,
  },
  {
    id: 6,
    name: "تابلت iPad Air",
    price: 21000,
    image: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=500&h=500&fit=crop",
    description: "iPad Air مع شاشة 10.9 بوصة ومعالج M1. مثالي للإبداع والإنتاجية مع دعم Apple Pencil.",
    category: "تابلت",
    stock: 12,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    merchantId: 2,
  },
];

export const ProductsProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(productsReducer, {
    products: mockExtendedProducts,
    categories: [...new Set(mockExtendedProducts.map(p => p.category))],
  });

  const addProduct = (productData: Omit<ExtendedProduct, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProduct: ExtendedProduct = {
      ...productData,
      id: Math.max(...state.products.map(p => p.id), 0) + 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    dispatch({ type: 'ADD_PRODUCT', product: newProduct });
  };

  const updateProduct = (product: ExtendedProduct) => {
    const updatedProduct = { ...product, updatedAt: new Date() };
    dispatch({ type: 'UPDATE_PRODUCT', product: updatedProduct });
  };

  const deleteProduct = (productId: number) => {
    dispatch({ type: 'DELETE_PRODUCT', productId });
  };

  const toggleProductStatus = (productId: number) => {
    dispatch({ type: 'TOGGLE_PRODUCT_STATUS', productId });
  };

  const updateStock = (productId: number, stock: number) => {
    dispatch({ type: 'UPDATE_STOCK', productId, stock });
  };

  const getProductsByMerchant = (merchantId: number) => {
    return state.products.filter(product => product.merchantId === merchantId);
  };

  return (
    <ProductsContext.Provider value={{ 
      state, 
      dispatch, 
      addProduct, 
      updateProduct, 
      deleteProduct, 
      toggleProductStatus, 
      updateStock,
      getProductsByMerchant,
    }}>
      {children}
    </ProductsContext.Provider>
  );
};

export const useProducts = () => {
  const context = useContext(ProductsContext);
  if (!context) {
    throw new Error('useProducts must be used within a ProductsProvider');
  }
  return context;
};
