import React, { createContext, useContext, useReducer, ReactNode } from 'react';

export interface ChatMessage {
  id: number;
  senderId: number;
  senderName: string;
  senderRole: 'admin' | 'merchant' | 'customer';
  receiverId: number;
  receiverName: string;
  receiverRole: 'admin' | 'merchant' | 'customer';
  message: string;
  timestamp: Date;
  isRead: boolean;
  type: 'text' | 'image' | 'file';
}

export interface ChatConversation {
  id: string;
  participants: {
    id: number;
    name: string;
    role: 'admin' | 'merchant' | 'customer';
  }[];
  lastMessage?: ChatMessage;
  unreadCount: number;
  updatedAt: Date;
}

interface ChatState {
  conversations: ChatConversation[];
  messages: { [conversationId: string]: ChatMessage[] };
  activeConversation: string | null;
  unreadTotal: number;
}

type ChatAction = 
  | { type: 'SET_CONVERSATIONS'; conversations: ChatConversation[] }
  | { type: 'SET_MESSAGES'; conversationId: string; messages: ChatMessage[] }
  | { type: 'ADD_MESSAGE'; conversationId: string; message: ChatMessage }
  | { type: 'SET_ACTIVE_CONVERSATION'; conversationId: string | null }
  | { type: 'MARK_AS_READ'; conversationId: string; userId: number }
  | { type: 'UPDATE_UNREAD_COUNT' };

const ChatContext = createContext<{
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;
  sendMessage: (conversationId: string, message: string, senderId: number, receiverId: number) => void;
  createConversation: (participant1: any, participant2: any) => string;
  markAsRead: (conversationId: string, userId: number) => void;
  getConversationId: (userId1: number, userId2: number) => string;
} | null>(null);

const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SET_CONVERSATIONS':
      return {
        ...state,
        conversations: action.conversations,
      };
    case 'SET_MESSAGES':
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.conversationId]: action.messages,
        },
      };
    case 'ADD_MESSAGE':
      const updatedMessages = {
        ...state.messages,
        [action.conversationId]: [
          ...(state.messages[action.conversationId] || []),
          action.message,
        ],
      };
      
      // Update conversation last message and unread count
      const updatedConversations = state.conversations.map(conv => {
        if (conv.id === action.conversationId) {
          return {
            ...conv,
            lastMessage: action.message,
            unreadCount: conv.unreadCount + 1,
            updatedAt: action.message.timestamp,
          };
        }
        return conv;
      });

      return {
        ...state,
        messages: updatedMessages,
        conversations: updatedConversations,
        unreadTotal: state.unreadTotal + 1,
      };
    case 'SET_ACTIVE_CONVERSATION':
      return {
        ...state,
        activeConversation: action.conversationId,
      };
    case 'MARK_AS_READ':
      const readMessages = state.messages[action.conversationId]?.map(msg => 
        msg.receiverId === action.userId ? { ...msg, isRead: true } : msg
      ) || [];
      
      const readConversations = state.conversations.map(conv => {
        if (conv.id === action.conversationId) {
          const unreadCount = readMessages.filter(msg => 
            msg.receiverId === action.userId && !msg.isRead
          ).length;
          return { ...conv, unreadCount };
        }
        return conv;
      });

      return {
        ...state,
        messages: {
          ...state.messages,
          [action.conversationId]: readMessages,
        },
        conversations: readConversations,
      };
    case 'UPDATE_UNREAD_COUNT':
      const total = state.conversations.reduce((sum, conv) => sum + conv.unreadCount, 0);
      return {
        ...state,
        unreadTotal: total,
      };
    default:
      return state;
  }
};

// Mock data
const mockConversations: ChatConversation[] = [
  {
    id: 'admin-merchant-2',
    participants: [
      { id: 1, name: 'المدير العام', role: 'admin' },
      { id: 2, name: 'أحمد التاجر', role: 'merchant' },
    ],
    unreadCount: 0,
    updatedAt: new Date(),
  },
];

const mockMessages: { [key: string]: ChatMessage[] } = {
  'admin-merchant-2': [
    {
      id: 1,
      senderId: 1,
      senderName: 'المدير العام',
      senderRole: 'admin',
      receiverId: 2,
      receiverName: 'أحمد التاجر',
      receiverRole: 'merchant',
      message: 'مرحباً أحمد، كيف يمكنني مساعدتك؟',
      timestamp: new Date(Date.now() - 3600000),
      isRead: true,
      type: 'text',
    },
    {
      id: 2,
      senderId: 2,
      senderName: 'أحمد التاجر',
      senderRole: 'merchant',
      receiverId: 1,
      receiverName: 'المدير العام',
      receiverRole: 'admin',
      message: 'مرحباً، أريد الاستفسار عن إضافة منتجات جديدة',
      timestamp: new Date(Date.now() - 3000000),
      isRead: true,
      type: 'text',
    },
  ],
};

export const ChatProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(chatReducer, {
    conversations: mockConversations,
    messages: mockMessages,
    activeConversation: null,
    unreadTotal: 0,
  });

  const getConversationId = (userId1: number, userId2: number): string => {
    const sortedIds = [userId1, userId2].sort();
    return `user-${sortedIds[0]}-${sortedIds[1]}`;
  };

  const createConversation = (participant1: any, participant2: any): string => {
    const conversationId = getConversationId(participant1.id, participant2.id);
    
    const existingConv = state.conversations.find(conv => conv.id === conversationId);
    if (existingConv) {
      return conversationId;
    }

    const newConversation: ChatConversation = {
      id: conversationId,
      participants: [participant1, participant2],
      unreadCount: 0,
      updatedAt: new Date(),
    };

    dispatch({
      type: 'SET_CONVERSATIONS',
      conversations: [...state.conversations, newConversation],
    });

    return conversationId;
  };

  const sendMessage = (conversationId: string, message: string, senderId: number, receiverId: number) => {
    const conversation = state.conversations.find(conv => conv.id === conversationId);
    if (!conversation) return;

    const sender = conversation.participants.find(p => p.id === senderId);
    const receiver = conversation.participants.find(p => p.id === receiverId);
    
    if (!sender || !receiver) return;

    const newMessage: ChatMessage = {
      id: Date.now(),
      senderId,
      senderName: sender.name,
      senderRole: sender.role,
      receiverId,
      receiverName: receiver.name,
      receiverRole: receiver.role,
      message,
      timestamp: new Date(),
      isRead: false,
      type: 'text',
    };

    dispatch({
      type: 'ADD_MESSAGE',
      conversationId,
      message: newMessage,
    });
  };

  const markAsRead = (conversationId: string, userId: number) => {
    dispatch({
      type: 'MARK_AS_READ',
      conversationId,
      userId,
    });
  };

  return (
    <ChatContext.Provider value={{
      state,
      dispatch,
      sendMessage,
      createConversation,
      markAsRead,
      getConversationId,
    }}>
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
