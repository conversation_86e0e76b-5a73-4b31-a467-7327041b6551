import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';

export interface Notification {
  id: string;
  type: 'chat' | 'order' | 'system' | 'warning';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: Date;
  fromUserId?: string;
  fromUserName?: string;
  fromUserRole?: 'admin' | 'merchant' | 'customer';
  actionUrl?: string;
}

interface NotificationsState {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
}

type NotificationsAction = 
  | { type: 'SET_NOTIFICATIONS'; notifications: Notification[] }
  | { type: 'ADD_NOTIFICATION'; notification: Notification }
  | { type: 'MARK_AS_READ'; notificationId: string }
  | { type: 'MARK_ALL_AS_READ' }
  | { type: 'DELETE_NOTIFICATION'; notificationId: string }
  | { type: 'SET_LOADING'; loading: boolean };

const notificationsReducer = (state: NotificationsState, action: NotificationsAction): NotificationsState => {
  switch (action.type) {
    case 'SET_NOTIFICATIONS':
      return {
        ...state,
        notifications: action.notifications,
        unreadCount: action.notifications.filter(n => !n.isRead).length,
        loading: false,
      };
    case 'ADD_NOTIFICATION':
      const newNotifications = [action.notification, ...state.notifications];
      return {
        ...state,
        notifications: newNotifications,
        unreadCount: newNotifications.filter(n => !n.isRead).length,
      };
    case 'MARK_AS_READ':
      const updatedNotifications = state.notifications.map(notification =>
        notification.id === action.notificationId
          ? { ...notification, isRead: true }
          : notification
      );
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.isRead).length,
      };
    case 'MARK_ALL_AS_READ':
      const allReadNotifications = state.notifications.map(notification => ({
        ...notification,
        isRead: true,
      }));
      return {
        ...state,
        notifications: allReadNotifications,
        unreadCount: 0,
      };
    case 'DELETE_NOTIFICATION':
      const filteredNotifications = state.notifications.filter(n => n.id !== action.notificationId);
      return {
        ...state,
        notifications: filteredNotifications,
        unreadCount: filteredNotifications.filter(n => !n.isRead).length,
      };
    case 'SET_LOADING':
      return { ...state, loading: action.loading };
    default:
      return state;
  }
};

const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  loading: false,
};

const NotificationsContext = createContext<{
  state: NotificationsState;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'isRead'>) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (notificationId: string) => void;
  createChatNotification: (fromUserId: string, fromUserName: string, fromUserRole: string, message: string) => void;
} | null>(null);

export const NotificationsProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(notificationsReducer, initialState);
  const { state: authState } = useAuth();

  // محاكاة تحميل الإشعارات من قاعدة البيانات
  useEffect(() => {
    if (authState.isAuthenticated && authState.user) {
      loadNotifications();
    }
  }, [authState.isAuthenticated, authState.user]);

  const loadNotifications = () => {
    // هنا سيتم تحميل الإشعارات من Firebase
    // حالياً سنضع بعض الإشعارات التجريبية للمدير فقط
    if (authState.user?.role === 'admin') {
      const mockNotifications: Notification[] = [
        {
          id: '1',
          type: 'system',
          title: 'مرحباً بك في Easy Shop',
          message: 'تم تفعيل نظام الإشعارات بنجاح',
          isRead: false,
          createdAt: new Date(),
        },
      ];
      dispatch({ type: 'SET_NOTIFICATIONS', notifications: mockNotifications });
    } else {
      dispatch({ type: 'SET_NOTIFICATIONS', notifications: [] });
    }
  };

  const addNotification = (notificationData: Omit<Notification, 'id' | 'createdAt' | 'isRead'>) => {
    const notification: Notification = {
      ...notificationData,
      id: Date.now().toString(),
      createdAt: new Date(),
      isRead: false,
    };
    dispatch({ type: 'ADD_NOTIFICATION', notification });
  };

  const markAsRead = (notificationId: string) => {
    dispatch({ type: 'MARK_AS_READ', notificationId });
    // هنا سيتم تحديث حالة القراءة في Firebase
  };

  const markAllAsRead = () => {
    dispatch({ type: 'MARK_ALL_AS_READ' });
    // هنا سيتم تحديث جميع الإشعارات في Firebase
  };

  const deleteNotification = (notificationId: string) => {
    dispatch({ type: 'DELETE_NOTIFICATION', notificationId });
    // هنا سيتم حذف الإشعار من Firebase
  };

  const createChatNotification = (fromUserId: string, fromUserName: string, fromUserRole: string, message: string) => {
    // إنشاء إشعار للشات - سيتم إرساله للمدير فقط
    if (authState.user?.role === 'admin' && fromUserId !== authState.user.id) {
      addNotification({
        type: 'chat',
        title: `رسالة جديدة من ${fromUserName}`,
        message: message.length > 50 ? message.substring(0, 50) + '...' : message,
        fromUserId,
        fromUserName,
        fromUserRole: fromUserRole as any,
        actionUrl: '/admin#chat',
      });
    }
  };

  return (
    <NotificationsContext.Provider value={{
      state,
      addNotification,
      markAsRead,
      markAllAsRead,
      deleteNotification,
      createChatNotification,
    }}>
      {children}
    </NotificationsContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationsContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }
  return context;
};
