import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { adService, Ad } from '@/services/firebaseService';

// إعادة تصدير نوع Ad من الخدمة
export type { Ad } from '@/services/firebaseService';

interface AdsState {
  ads: Ad[];
  activeAds: Ad[];
  loading: boolean;
  error: string | null;
}

type AdsAction =
  | { type: 'SET_LOADING'; loading: boolean }
  | { type: 'SET_ERROR'; error: string | null }
  | { type: 'SET_ADS'; ads: Ad[] }
  | { type: 'ADD_AD'; ad: Ad }
  | { type: 'UPDATE_AD'; ad: Ad }
  | { type: 'DELETE_AD'; adId: string };

const AdsContext = createContext<{
  state: AdsState;
  addAd: (ad: Omit<Ad, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateAd: (id: string, ad: Partial<Ad>) => Promise<void>;
  deleteAd: (adId: string) => Promise<void>;
  toggleAdStatus: (adId: string) => Promise<void>;
  refreshAds: () => Promise<void>;
} | null>(null);

const adsReducer = (state: AdsState, action: AdsAction): AdsState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.loading };
    case 'SET_ERROR':
      return { ...state, error: action.error, loading: false };
    case 'SET_ADS':
      return {
        ...state,
        ads: action.ads,
        activeAds: action.ads.filter(ad => ad.isActive),
        loading: false,
        error: null,
      };
    case 'ADD_AD':
      const newAds = [...state.ads, action.ad];
      return {
        ...state,
        ads: newAds,
        activeAds: newAds.filter(ad => ad.isActive),
      };
    case 'UPDATE_AD':
      const updatedAds = state.ads.map(ad =>
        ad.id === action.ad.id ? action.ad : ad
      );
      return {
        ...state,
        ads: updatedAds,
        activeAds: updatedAds.filter(ad => ad.isActive),
      };
    case 'DELETE_AD':
      const filteredAds = state.ads.filter(ad => ad.id !== action.adId);
      return {
        ...state,
        ads: filteredAds,
        activeAds: filteredAds.filter(ad => ad.isActive),
      };
    default:
      return state;
  }
};

// الحالة الأولية للإعلانات (فارغة)
const initialState: AdsState = {
  ads: [],
  activeAds: [],
  loading: false,
  error: null,
};

export const AdsProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(adsReducer, initialState);

  // تحميل الإعلانات عند بدء التطبيق
  useEffect(() => {
    refreshAds();
  }, []);

  const refreshAds = async () => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      const ads = await adService.getAll();
      dispatch({ type: 'SET_ADS', ads });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', error: 'فشل في تحميل الإعلانات' });
      console.error('Error loading ads:', error);
    }
  };

  const addAd = async (adData: Omit<Ad, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      const id = await adService.add(adData);
      const newAd: Ad = {
        ...adData,
        id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      dispatch({ type: 'ADD_AD', ad: newAd });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', error: 'فشل في إضافة الإعلان' });
      console.error('Error adding ad:', error);
      throw error;
    }
  };

  const updateAd = async (id: string, adData: Partial<Ad>) => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      await adService.update(id, adData);
      const updatedAd = {
        ...state.ads.find(ad => ad.id === id)!,
        ...adData,
        updatedAt: new Date(),
      };
      dispatch({ type: 'UPDATE_AD', ad: updatedAd });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', error: 'فشل في تحديث الإعلان' });
      console.error('Error updating ad:', error);
      throw error;
    }
  };

  const deleteAd = async (adId: string) => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      await adService.delete(adId);
      dispatch({ type: 'DELETE_AD', adId });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', error: 'فشل في حذف الإعلان' });
      console.error('Error deleting ad:', error);
      throw error;
    }
  };

  const toggleAdStatus = async (adId: string) => {
    try {
      const ad = state.ads.find(a => a.id === adId);
      if (ad) {
        await updateAd(adId, { isActive: !ad.isActive });
      }
    } catch (error) {
      console.error('Error toggling ad status:', error);
      throw error;
    }
  };

  return (
    <AdsContext.Provider value={{
      state,
      addAd,
      updateAd,
      deleteAd,
      toggleAdStatus,
      refreshAds,
    }}>
      {children}
    </AdsContext.Provider>
  );
};

export const useAds = () => {
  const context = useContext(AdsContext);
  if (!context) {
    throw new Error('useAds must be used within an AdsProvider');
  }
  return context;
};
