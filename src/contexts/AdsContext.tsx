import React, { createContext, useContext, useReducer, ReactNode } from 'react';

export interface Advertisement {
  id: number;
  title: string;
  description: string;
  image: string;
  link?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AdsState {
  ads: Advertisement[];
  activeAds: Advertisement[];
}

type AdsAction = 
  | { type: 'SET_ADS'; ads: Advertisement[] }
  | { type: 'ADD_AD'; ad: Advertisement }
  | { type: 'UPDATE_AD'; ad: Advertisement }
  | { type: 'DELETE_AD'; adId: number }
  | { type: 'TOGGLE_AD_STATUS'; adId: number };

const AdsContext = createContext<{
  state: AdsState;
  dispatch: React.Dispatch<AdsAction>;
  addAd: (ad: Omit<Advertisement, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateAd: (ad: Advertisement) => void;
  deleteAd: (adId: number) => void;
  toggleAdStatus: (adId: number) => void;
} | null>(null);

const adsReducer = (state: AdsState, action: AdsAction): AdsState => {
  switch (action.type) {
    case 'SET_ADS':
      return {
        ...state,
        ads: action.ads,
        activeAds: action.ads.filter(ad => ad.isActive),
      };
    case 'ADD_AD':
      const newAds = [...state.ads, action.ad];
      return {
        ...state,
        ads: newAds,
        activeAds: newAds.filter(ad => ad.isActive),
      };
    case 'UPDATE_AD':
      const updatedAds = state.ads.map(ad => 
        ad.id === action.ad.id ? action.ad : ad
      );
      return {
        ...state,
        ads: updatedAds,
        activeAds: updatedAds.filter(ad => ad.isActive),
      };
    case 'DELETE_AD':
      const filteredAds = state.ads.filter(ad => ad.id !== action.adId);
      return {
        ...state,
        ads: filteredAds,
        activeAds: filteredAds.filter(ad => ad.isActive),
      };
    case 'TOGGLE_AD_STATUS':
      const toggledAds = state.ads.map(ad => 
        ad.id === action.adId 
          ? { ...ad, isActive: !ad.isActive, updatedAt: new Date() }
          : ad
      );
      return {
        ...state,
        ads: toggledAds,
        activeAds: toggledAds.filter(ad => ad.isActive),
      };
    default:
      return state;
  }
};

// Mock advertisements
const mockAds: Advertisement[] = [
  {
    id: 1,
    title: 'عرض خاص على اللابتوبات',
    description: 'خصم 20% على جميع أجهزة اللابتوب لفترة محدودة',
    image: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=800&h=400&fit=crop',
    link: '/products',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 2,
    title: 'أحدث الهواتف الذكية',
    description: 'اكتشف أحدث موديلات الهواتف الذكية بأفضل الأسعار',
    image: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=800&h=400&fit=crop',
    link: '/products',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 3,
    title: 'إكسسوارات تقنية متنوعة',
    description: 'مجموعة واسعة من الإكسسوارات التقنية عالية الجودة',
    image: 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=800&h=400&fit=crop',
    link: '/products',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export const AdsProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(adsReducer, {
    ads: mockAds,
    activeAds: mockAds.filter(ad => ad.isActive),
  });

  const addAd = (adData: Omit<Advertisement, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newAd: Advertisement = {
      ...adData,
      id: Math.max(...state.ads.map(ad => ad.id), 0) + 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    dispatch({ type: 'ADD_AD', ad: newAd });
  };

  const updateAd = (ad: Advertisement) => {
    const updatedAd = { ...ad, updatedAt: new Date() };
    dispatch({ type: 'UPDATE_AD', ad: updatedAd });
  };

  const deleteAd = (adId: number) => {
    dispatch({ type: 'DELETE_AD', adId });
  };

  const toggleAdStatus = (adId: number) => {
    dispatch({ type: 'TOGGLE_AD_STATUS', adId });
  };

  return (
    <AdsContext.Provider value={{ 
      state, 
      dispatch, 
      addAd, 
      updateAd, 
      deleteAd, 
      toggleAdStatus 
    }}>
      {children}
    </AdsContext.Provider>
  );
};

export const useAds = () => {
  const context = useContext(AdsContext);
  if (!context) {
    throw new Error('useAds must be used within an AdsProvider');
  }
  return context;
};
