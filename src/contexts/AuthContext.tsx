import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';

export type UserRole = 'admin' | 'merchant' | 'customer';

export interface User {
  id: number;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

type AuthAction = 
  | { type: 'LOGIN'; user: User }
  | { type: 'LOGOUT' }
  | { type: 'SET_LOADING'; loading: boolean }
  | { type: 'UPDATE_USER'; user: User };

const AuthContext = createContext<{
  state: AuthState;
  dispatch: React.Dispatch<AuthAction>;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  register: (name: string, email: string, password: string, role?: UserRole) => Promise<boolean>;
} | null>(null);

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN':
      return {
        ...state,
        user: action.user,
        isAuthenticated: true,
        isLoading: false,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.loading,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.user,
      };
    default:
      return state;
  }
};

// مستخدم المدير الافتراضي الوحيد (لإعداد النظام)
const defaultAdmin: User = {
  id: 1,
  name: 'مدير النظام',
  email: '<EMAIL>',
  role: 'admin',
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(authReducer, {
    user: null,
    isAuthenticated: false,
    isLoading: true,
  });

  useEffect(() => {
    // Check for stored user session
    const storedUser = localStorage.getItem('easyshop_user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        dispatch({ type: 'LOGIN', user });
      } catch (error) {
        localStorage.removeItem('easyshop_user');
      }
    }
    dispatch({ type: 'SET_LOADING', loading: false });
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', loading: true });

    // محاكاة استدعاء API
    await new Promise(resolve => setTimeout(resolve, 1000));

    // السماح بدخول المدير الافتراضي فقط لإعداد النظام
    if (email === defaultAdmin.email && password === 'admin123') {
      localStorage.setItem('easyshop_user', JSON.stringify(defaultAdmin));
      dispatch({ type: 'LOGIN', user: defaultAdmin });
      return true;
    }

    // هنا سيتم ربط Firebase Authentication لاحقاً
    dispatch({ type: 'SET_LOADING', loading: false });
    return false;
  };

  const register = async (name: string, email: string, password: string, role: UserRole = 'customer'): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', loading: true });

    // محاكاة استدعاء API
    await new Promise(resolve => setTimeout(resolve, 1000));

    // هنا سيتم ربط Firebase Authentication لاحقاً
    // حالياً التسجيل معطل حتى يتم ربط Firebase
    dispatch({ type: 'SET_LOADING', loading: false });
    return false;
  };

  const logout = () => {
    localStorage.removeItem('easyshop_user');
    dispatch({ type: 'LOGOUT' });
  };

  return (
    <AuthContext.Provider value={{ state, dispatch, login, logout, register }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
