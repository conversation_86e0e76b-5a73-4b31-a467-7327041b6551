import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';

export type UserRole = 'admin' | 'merchant' | 'customer';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  createdAt: Date;
  isActive: boolean;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

type AuthAction = 
  | { type: 'LOGIN'; user: User }
  | { type: 'LOGOUT' }
  | { type: 'SET_LOADING'; loading: boolean }
  | { type: 'UPDATE_USER'; user: User };

const AuthContext = createContext<{
  state: AuthState;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (name: string, email: string, password: string, role?: UserRole) => Promise<boolean>;
  createUserProfile: (firebaseUser: FirebaseUser, userData: Partial<User>) => Promise<void>;
} | null>(null);

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN':
      return {
        ...state,
        user: action.user,
        isAuthenticated: true,
        isLoading: false,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.loading,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.user,
      };
    default:
      return state;
  }
};

// وظائف مساعدة للتعامل مع بيانات المستخدمين
const getUserProfile = async (uid: string): Promise<User | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', uid));
    if (userDoc.exists()) {
      const data = userDoc.data();
      return {
        id: uid,
        name: data.name,
        email: data.email,
        role: data.role,
        avatar: data.avatar,
        createdAt: data.createdAt?.toDate() || new Date(),
        isActive: data.isActive ?? true,
      };
    }
    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(authReducer, {
    user: null,
    isAuthenticated: false,
    isLoading: true,
  });

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        dispatch({ type: 'SET_LOADING', loading: true });
        const userProfile = await getUserProfile(firebaseUser.uid);
        if (userProfile) {
          dispatch({ type: 'LOGIN', user: userProfile });
        } else {
          dispatch({ type: 'SET_LOADING', loading: false });
        }
      } else {
        dispatch({ type: 'LOGOUT' });
      }
    });

    return () => unsubscribe();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const userProfile = await getUserProfile(userCredential.user.uid);

      if (userProfile && userProfile.isActive) {
        dispatch({ type: 'LOGIN', user: userProfile });
        return true;
      } else {
        await signOut(auth);
        dispatch({ type: 'SET_LOADING', loading: false });
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      dispatch({ type: 'SET_LOADING', loading: false });
      return false;
    }
  };

  const register = async (name: string, email: string, password: string, role: UserRole = 'customer'): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_LOADING', loading: true });
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);

      const userData: User = {
        id: userCredential.user.uid,
        name,
        email,
        role,
        createdAt: new Date(),
        isActive: true,
      };

      await createUserProfile(userCredential.user, userData);
      dispatch({ type: 'LOGIN', user: userData });
      return true;
    } catch (error) {
      console.error('Registration error:', error);
      dispatch({ type: 'SET_LOADING', loading: false });
      return false;
    }
  };

  const createUserProfile = async (firebaseUser: FirebaseUser, userData: Partial<User>) => {
    try {
      await setDoc(doc(db, 'users', firebaseUser.uid), {
        name: userData.name,
        email: userData.email,
        role: userData.role,
        createdAt: new Date(),
        isActive: true,
        avatar: userData.avatar || null,
      });
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <AuthContext.Provider value={{
      state,
      login,
      logout,
      register,
      createUserProfile
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
