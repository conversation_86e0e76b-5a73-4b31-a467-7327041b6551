import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';

export type UserRole = 'admin' | 'merchant' | 'customer';

export interface User {
  id: number;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

type AuthAction = 
  | { type: 'LOGIN'; user: User }
  | { type: 'LOGOUT' }
  | { type: 'SET_LOADING'; loading: boolean }
  | { type: 'UPDATE_USER'; user: User };

const AuthContext = createContext<{
  state: AuthState;
  dispatch: React.Dispatch<AuthAction>;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  register: (name: string, email: string, password: string, role?: UserRole) => Promise<boolean>;
} | null>(null);

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN':
      return {
        ...state,
        user: action.user,
        isAuthenticated: true,
        isLoading: false,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.loading,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.user,
      };
    default:
      return state;
  }
};

// Mock users for demonstration
const mockUsers: User[] = [
  {
    id: 1,
    name: 'المدير العام',
    email: '<EMAIL>',
    role: 'admin',
  },
  {
    id: 2,
    name: 'أحمد التاجر',
    email: '<EMAIL>',
    role: 'merchant',
  },
  {
    id: 3,
    name: 'محمد الزبون',
    email: '<EMAIL>',
    role: 'customer',
  },
];

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(authReducer, {
    user: null,
    isAuthenticated: false,
    isLoading: true,
  });

  useEffect(() => {
    // Check for stored user session
    const storedUser = localStorage.getItem('easyshop_user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        dispatch({ type: 'LOGIN', user });
      } catch (error) {
        localStorage.removeItem('easyshop_user');
      }
    }
    dispatch({ type: 'SET_LOADING', loading: false });
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', loading: true });
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const user = mockUsers.find(u => u.email === email);
    if (user && password === '123456') { // Simple password for demo
      localStorage.setItem('easyshop_user', JSON.stringify(user));
      dispatch({ type: 'LOGIN', user });
      return true;
    }
    
    dispatch({ type: 'SET_LOADING', loading: false });
    return false;
  };

  const register = async (name: string, email: string, password: string, role: UserRole = 'customer'): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', loading: true });
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const existingUser = mockUsers.find(u => u.email === email);
    if (existingUser) {
      dispatch({ type: 'SET_LOADING', loading: false });
      return false;
    }
    
    const newUser: User = {
      id: mockUsers.length + 1,
      name,
      email,
      role,
    };
    
    mockUsers.push(newUser);
    localStorage.setItem('easyshop_user', JSON.stringify(newUser));
    dispatch({ type: 'LOGIN', user: newUser });
    return true;
  };

  const logout = () => {
    localStorage.removeItem('easyshop_user');
    dispatch({ type: 'LOGOUT' });
  };

  return (
    <AuthContext.Provider value={{ state, dispatch, login, logout, register }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
