# 🛒 Easy Shop - متجر إلكتروني متكامل

## 📋 **وصف المشروع**

Easy Shop هو نظام متجر إلكتروني متكامل مبني بتقنيات حديثة، يوفر تجربة تسوق سلسة ولوحة تحكم شاملة للإدارة والتجار.

## ✨ **المميزات الرئيسية**

### 🎯 **للمدير:**
- ✅ **لوحة تحكم شاملة** مع إحصائيات مفصلة
- ✅ **إدارة المنتجات** - إضافة/تعديل/حذف/إخفاء
- ✅ **إدارة الإعلانات** - تحكم كامل في الإعلانات الرئيسية
- ✅ **إدارة المستخدمين** - إضافة وإدارة التجار والزبائن
- ✅ **إعدادات المتجر الشاملة** - تخصيص كامل للمتجر
- ✅ **نظام شات داخلي** - التواصل مع التجار والزبائن
- ✅ **رفع الصور** - من الجهاز مباشرة
- ✅ **إدارة الفئات** - إضافة فئات مخصصة

### 🏪 **للتاجر:**
- ✅ **لوحة تحكم مخصصة** مع إحصائيات المنتجات
- ✅ **إدارة المنتجات الخاصة** - إضافة وتعديل منتجاته
- ✅ **مراقبة المخزون** - تتبع الكميات والمبيعات
- ✅ **إعدادات الحساب** - تعديل المعلومات الشخصية

### 🛍️ **للزبون:**
- ✅ **تجربة تسوق سلسة** - تصفح وشراء المنتجات
- ✅ **سلة التسوق** - إدارة المشتريات
- ✅ **إعدادات الحساب** - إدارة المعلومات الشخصية

## 🛠️ **التقنيات المستخدمة**

### **Frontend:**
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - للكتابة الآمنة
- **Tailwind CSS** - للتصميم
- **Shadcn/ui** - مكونات واجهة المستخدم
- **React Router** - للتنقل
- **Lucide React** - الأيقونات

### **Backend & Database:**
- **Firebase Firestore** - قاعدة البيانات
- **Firebase Storage** - تخزين الصور
- **Firebase Authentication** - نظام المصادقة

## 🚀 **التثبيت والتشغيل**

### **1. تثبيت التبعيات:**
```bash
npm install
```

### **2. إعداد Firebase:**
اتبع الإرشادات في ملف `FIREBASE_SETUP.md`

### **3. تشغيل التطبيق:**
```bash
npm run dev
```

التطبيق سيعمل على: `http://localhost:8080`

## 🔐 **تسجيل الدخول الأول**

### **حساب المدير الافتراضي:**
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `admin123`

> ⚠️ **مهم:** غيّر كلمة المرور بعد أول تسجيل دخول

## 📁 **هيكل المشروع**

```
src/
├── components/          # مكونات واجهة المستخدم
│   ├── admin/          # مكونات لوحة تحكم المدير
│   ├── ui/             # مكونات واجهة أساسية
│   └── ...
├── contexts/           # سياقات React (State Management)
├── pages/              # صفحات التطبيق
├── services/           # خدمات Firebase
├── lib/                # مكتبات مساعدة
└── hooks/              # React Hooks مخصصة
```

## 🎨 **التخصيص**

### **إعدادات المتجر:**
1. سجل دخول كمدير
2. اذهب إلى "الإعدادات"
3. خصص:
   - اسم المتجر وشعاره
   - الألوان والتصميم
   - المميزات السفلية
   - طرق الدفع والشحن
   - معلومات التواصل

### **إضافة منتجات:**
1. اذهب إلى "إدارة المنتجات"
2. انقر "إضافة منتج جديد"
3. املأ التفاصيل ورفع الصور
4. اختر الفئة أو أضف فئة جديدة

## 📊 **قاعدة البيانات**

### **Collections في Firestore:**
- `products` - المنتجات
- `ads` - الإعلانات
- `users` - المستخدمين
- `categories` - فئات المنتجات
- `settings` - إعدادات المتجر

### **Storage في Firebase:**
- `products/` - صور المنتجات
- `ads/` - صور الإعلانات
- `logos/` - شعارات المتجر

## 🔧 **الأوامر المتاحة**

```bash
npm run dev          # تشغيل التطبيق في وضع التطوير
npm run build        # بناء التطبيق للإنتاج
npm run preview      # معاينة النسخة المبنية
npm run lint         # فحص الكود
```

## 🌟 **المميزات المتقدمة**

### **نظام الشات:**
- شات مباشر بين المدير والمستخدمين
- إشعارات الرسائل غير المقروءة
- واجهة تفاعلية مع تاريخ المحادثات

### **رفع الصور:**
- رفع مباشر من الجهاز
- معاينة فورية
- تحويل تلقائي للصيغ المدعومة

### **إدارة الفئات:**
- إضافة فئات مخصصة
- فئات افتراضية للبدء السريع
- إدارة ديناميكية للفئات

## 🔒 **الأمان**

- مصادقة آمنة عبر Firebase
- قواعد أمان Firestore
- التحقق من الصلاحيات
- حماية المسارات الحساسة

## 📱 **التجاوب**

التطبيق مصمم ليعمل بشكل مثالي على:
- 💻 أجهزة الكمبيوتر
- 📱 الهواتف الذكية
- 📟 الأجهزة اللوحية

---

**تم تطوير هذا المشروع بـ ❤️ لتوفير تجربة تسوق إلكتروني متميزة**
